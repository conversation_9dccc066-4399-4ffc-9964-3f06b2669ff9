{"version": 3, "sources": ["../../node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js", "../../node_modules/use-sync-external-store/shim/index.js", "../../node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js", "../../node_modules/use-sync-external-store/shim/with-selector.js", "../../node_modules/zustand/esm/vanilla.mjs", "../../node_modules/zustand/esm/index.mjs"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    function useSyncExternalStore$2(subscribe, getSnapshot) {\n      didWarnOld18Alpha ||\n        void 0 === React.startTransition ||\n        ((didWarnOld18Alpha = !0),\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"\n        ));\n      var value = getSnapshot();\n      if (!didWarnUncachedGetSnapshot) {\n        var cachedValue = getSnapshot();\n        objectIs(value, cachedValue) ||\n          (console.error(\n            \"The result of getSnapshot should be cached to avoid an infinite loop\"\n          ),\n          (didWarnUncachedGetSnapshot = !0));\n      }\n      cachedValue = useState({\n        inst: { value: value, getSnapshot: getSnapshot }\n      });\n      var inst = cachedValue[0].inst,\n        forceUpdate = cachedValue[1];\n      useLayoutEffect(\n        function () {\n          inst.value = value;\n          inst.getSnapshot = getSnapshot;\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n        },\n        [subscribe, value, getSnapshot]\n      );\n      useEffect(\n        function () {\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          return subscribe(function () {\n            checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          });\n        },\n        [subscribe]\n      );\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      inst = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(inst, nextValue);\n      } catch (error) {\n        return !0;\n      }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n      return getSnapshot();\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue,\n      didWarnOld18Alpha = !1,\n      didWarnUncachedGetSnapshot = !1,\n      shim =\n        \"undefined\" === typeof window ||\n        \"undefined\" === typeof window.document ||\n        \"undefined\" === typeof window.document.createElement\n          ? useSyncExternalStore$1\n          : useSyncExternalStore$2;\n    exports.useSyncExternalStore =\n      void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      shim = require(\"use-sync-external-store/shim\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useSyncExternalStore = shim.useSyncExternalStore,\n      useRef = React.useRef,\n      useEffect = React.useEffect,\n      useMemo = React.useMemo,\n      useDebugValue = React.useDebugValue;\n    exports.useSyncExternalStoreWithSelector = function (\n      subscribe,\n      getSnapshot,\n      getServerSnapshot,\n      selector,\n      isEqual\n    ) {\n      var instRef = useRef(null);\n      if (null === instRef.current) {\n        var inst = { hasValue: !1, value: null };\n        instRef.current = inst;\n      } else inst = instRef.current;\n      instRef = useMemo(\n        function () {\n          function memoizedSelector(nextSnapshot) {\n            if (!hasMemo) {\n              hasMemo = !0;\n              memoizedSnapshot = nextSnapshot;\n              nextSnapshot = selector(nextSnapshot);\n              if (void 0 !== isEqual && inst.hasValue) {\n                var currentSelection = inst.value;\n                if (isEqual(currentSelection, nextSnapshot))\n                  return (memoizedSelection = currentSelection);\n              }\n              return (memoizedSelection = nextSnapshot);\n            }\n            currentSelection = memoizedSelection;\n            if (objectIs(memoizedSnapshot, nextSnapshot))\n              return currentSelection;\n            var nextSelection = selector(nextSnapshot);\n            if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))\n              return (memoizedSnapshot = nextSnapshot), currentSelection;\n            memoizedSnapshot = nextSnapshot;\n            return (memoizedSelection = nextSelection);\n          }\n          var hasMemo = !1,\n            memoizedSnapshot,\n            memoizedSelection,\n            maybeGetServerSnapshot =\n              void 0 === getServerSnapshot ? null : getServerSnapshot;\n          return [\n            function () {\n              return memoizedSelector(getSnapshot());\n            },\n            null === maybeGetServerSnapshot\n              ? void 0\n              : function () {\n                  return memoizedSelector(maybeGetServerSnapshot());\n                }\n          ];\n        },\n        [getSnapshot, getServerSnapshot, selector, isEqual]\n      );\n      var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n      useEffect(\n        function () {\n          inst.hasValue = !0;\n          inst.value = value;\n        },\n        [value]\n      );\n      useDebugValue(value);\n      return value;\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}\n", "const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const destroy = () => {\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n      console.warn(\n        \"[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected.\"\n      );\n    }\n    listeners.clear();\n  };\n  const api = { setState, getState, getInitialState, subscribe, destroy };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\nvar vanilla = (createState) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] Default export is deprecated. Instead use import { createStore } from 'zustand/vanilla'.\"\n    );\n  }\n  return createStore(createState);\n};\n\nexport { createStore, vanilla as default };\n", "import { createStore } from 'zustand/vanilla';\nexport * from 'zustand/vanilla';\nimport ReactExports from 'react';\nimport useSyncExternalStoreExports from 'use-sync-external-store/shim/with-selector.js';\n\nconst { useDebugValue } = ReactExports;\nconst { useSyncExternalStoreWithSelector } = useSyncExternalStoreExports;\nlet didWarnAboutEqualityFn = false;\nconst identity = (arg) => arg;\nfunction useStore(api, selector = identity, equalityFn) {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && equalityFn && !didWarnAboutEqualityFn) {\n    console.warn(\n      \"[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937\"\n    );\n    didWarnAboutEqualityFn = true;\n  }\n  const slice = useSyncExternalStoreWithSelector(\n    api.subscribe,\n    api.getState,\n    api.getServerState || api.getInitialState,\n    selector,\n    equalityFn\n  );\n  useDebugValue(slice);\n  return slice;\n}\nconst createImpl = (createState) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && typeof createState !== \"function\") {\n    console.warn(\n      \"[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.\"\n    );\n  }\n  const api = typeof createState === \"function\" ? createStore(createState) : createState;\n  const useBoundStore = (selector, equalityFn) => useStore(api, selector, equalityFn);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = (createState) => createState ? createImpl(createState) : createImpl;\nvar react = (createState) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] Default export is deprecated. Instead use `import { create } from 'zustand'`.\"\n    );\n  }\n  return create(createState);\n};\n\nexport { create, react as default, useStore };\n"], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA;AAWA,KACG,WAAY;AACX,eAAS,GAAG,GAAG,GAAG;AAChB,eAAQ,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAQ,MAAM,KAAK,MAAM;AAAA,MACxE;AACA,eAAS,uBAAuB,WAAW,aAAa;AACtD,6BACE,WAAW,MAAM,oBACf,oBAAoB,MACtB,QAAQ;AAAA,UACN;AAAA,QACF;AACF,YAAI,QAAQ,YAAY;AACxB,YAAI,CAAC,4BAA4B;AAC/B,cAAI,cAAc,YAAY;AAC9B,mBAAS,OAAO,WAAW,MACxB,QAAQ;AAAA,YACP;AAAA,UACF,GACC,6BAA6B;AAAA,QAClC;AACA,sBAAc,SAAS;AAAA,UACrB,MAAM,EAAE,OAAc,YAAyB;AAAA,QACjD,CAAC;AACD,YAAI,OAAO,YAAY,CAAC,EAAE,MACxB,cAAc,YAAY,CAAC;AAC7B;AAAA,UACE,WAAY;AACV,iBAAK,QAAQ;AACb,iBAAK,cAAc;AACnB,mCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAAA,UAC5D;AAAA,UACA,CAAC,WAAW,OAAO,WAAW;AAAA,QAChC;AACA;AAAA,UACE,WAAY;AACV,mCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAC1D,mBAAO,UAAU,WAAY;AAC3B,qCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAAA,YAC5D,CAAC;AAAA,UACH;AAAA,UACA,CAAC,SAAS;AAAA,QACZ;AACA,QAAAA,eAAc,KAAK;AACnB,eAAO;AAAA,MACT;AACA,eAAS,uBAAuB,MAAM;AACpC,YAAI,oBAAoB,KAAK;AAC7B,eAAO,KAAK;AACZ,YAAI;AACF,cAAI,YAAY,kBAAkB;AAClC,iBAAO,CAAC,SAAS,MAAM,SAAS;AAAA,QAClC,SAAS,OAAO;AACd,iBAAO;AAAA,QACT;AAAA,MACF;AACA,eAAS,uBAAuB,WAAW,aAAa;AACtD,eAAO,YAAY;AAAA,MACrB;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,+BACxC,+BAA+B,4BAA4B,MAAM,CAAC;AACpE,UAAI,QAAQ,iBACV,WAAW,eAAe,OAAO,OAAO,KAAK,OAAO,KAAK,IACzD,WAAW,MAAM,UACjB,YAAY,MAAM,WAClB,kBAAkB,MAAM,iBACxBA,iBAAgB,MAAM,eACtB,oBAAoB,OACpB,6BAA6B,OAC7B,OACE,gBAAgB,OAAO,UACvB,gBAAgB,OAAO,OAAO,YAC9B,gBAAgB,OAAO,OAAO,SAAS,gBACnC,yBACA;AACR,cAAQ,uBACN,WAAW,MAAM,uBAAuB,MAAM,uBAAuB;AACvE,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,8BACxC,+BAA+B,2BAA2B,MAAM,CAAC;AAAA,IACrE,GAAG;AAAA;AAAA;;;AC9FL;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAWA,KACG,WAAY;AACX,eAAS,GAAG,GAAG,GAAG;AAChB,eAAQ,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAQ,MAAM,KAAK,MAAM;AAAA,MACxE;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,+BACxC,+BAA+B,4BAA4B,MAAM,CAAC;AACpE,UAAI,QAAQ,iBACV,OAAO,gBACP,WAAW,eAAe,OAAO,OAAO,KAAK,OAAO,KAAK,IACzD,uBAAuB,KAAK,sBAC5B,SAAS,MAAM,QACf,YAAY,MAAM,WAClB,UAAU,MAAM,SAChBC,iBAAgB,MAAM;AACxB,cAAQ,mCAAmC,SACzC,WACA,aACA,mBACA,UACA,SACA;AACA,YAAI,UAAU,OAAO,IAAI;AACzB,YAAI,SAAS,QAAQ,SAAS;AAC5B,cAAI,OAAO,EAAE,UAAU,OAAI,OAAO,KAAK;AACvC,kBAAQ,UAAU;AAAA,QACpB,MAAO,QAAO,QAAQ;AACtB,kBAAU;AAAA,UACR,WAAY;AACV,qBAAS,iBAAiB,cAAc;AACtC,kBAAI,CAAC,SAAS;AACZ,0BAAU;AACV,mCAAmB;AACnB,+BAAe,SAAS,YAAY;AACpC,oBAAI,WAAW,WAAW,KAAK,UAAU;AACvC,sBAAI,mBAAmB,KAAK;AAC5B,sBAAI,QAAQ,kBAAkB,YAAY;AACxC,2BAAQ,oBAAoB;AAAA,gBAChC;AACA,uBAAQ,oBAAoB;AAAA,cAC9B;AACA,iCAAmB;AACnB,kBAAI,SAAS,kBAAkB,YAAY;AACzC,uBAAO;AACT,kBAAI,gBAAgB,SAAS,YAAY;AACzC,kBAAI,WAAW,WAAW,QAAQ,kBAAkB,aAAa;AAC/D,uBAAQ,mBAAmB,cAAe;AAC5C,iCAAmB;AACnB,qBAAQ,oBAAoB;AAAA,YAC9B;AACA,gBAAI,UAAU,OACZ,kBACA,mBACA,yBACE,WAAW,oBAAoB,OAAO;AAC1C,mBAAO;AAAA,cACL,WAAY;AACV,uBAAO,iBAAiB,YAAY,CAAC;AAAA,cACvC;AAAA,cACA,SAAS,yBACL,SACA,WAAY;AACV,uBAAO,iBAAiB,uBAAuB,CAAC;AAAA,cAClD;AAAA,YACN;AAAA,UACF;AAAA,UACA,CAAC,aAAa,mBAAmB,UAAU,OAAO;AAAA,QACpD;AACA,YAAI,QAAQ,qBAAqB,WAAW,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AAClE;AAAA,UACE,WAAY;AACV,iBAAK,WAAW;AAChB,iBAAK,QAAQ;AAAA,UACf;AAAA,UACA,CAAC,KAAK;AAAA,QACR;AACA,QAAAA,eAAc,KAAK;AACnB,eAAO;AAAA,MACT;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,8BACxC,+BAA+B,2BAA2B,MAAM,CAAC;AAAA,IACrE,GAAG;AAAA;AAAA;;;AChGL;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA,IAAM,kBAAkB,CAAC,gBAAgB;AACvC,MAAI;AACJ,QAAM,YAA4B,oBAAI,IAAI;AAC1C,QAAM,WAAW,CAAC,SAAS,YAAY;AACrC,UAAM,YAAY,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AACnE,QAAI,CAAC,OAAO,GAAG,WAAW,KAAK,GAAG;AAChC,YAAM,gBAAgB;AACtB,eAAS,WAAW,OAAO,UAAU,OAAO,cAAc,YAAY,cAAc,QAAQ,YAAY,OAAO,OAAO,CAAC,GAAG,OAAO,SAAS;AAC1I,gBAAU,QAAQ,CAAC,aAAa,SAAS,OAAO,aAAa,CAAC;AAAA,IAChE;AAAA,EACF;AACA,QAAM,WAAW,MAAM;AACvB,QAAM,kBAAkB,MAAM;AAC9B,QAAM,YAAY,CAAC,aAAa;AAC9B,cAAU,IAAI,QAAQ;AACtB,WAAO,MAAM,UAAU,OAAO,QAAQ;AAAA,EACxC;AACA,QAAM,UAAU,MAAM;AACpB,SAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,cAAc;AACtE,cAAQ;AAAA,QACN;AAAA,MACF;AAAA,IACF;AACA,cAAU,MAAM;AAAA,EAClB;AACA,QAAM,MAAM,EAAE,UAAU,UAAU,iBAAiB,WAAW,QAAQ;AACtE,QAAM,eAAe,QAAQ,YAAY,UAAU,UAAU,GAAG;AAChE,SAAO;AACT;AACA,IAAM,cAAc,CAAC,gBAAgB,cAAc,gBAAgB,WAAW,IAAI;;;AC3BlF,mBAAyB;AACzB,2BAAwC;AAExC,IAAM,EAAE,cAAc,IAAI,aAAAC;AAC1B,IAAM,EAAE,iCAAiC,IAAI,qBAAAC;AAC7C,IAAI,yBAAyB;AAC7B,IAAM,WAAW,CAAC,QAAQ;AAC1B,SAAS,SAAS,KAAK,WAAW,UAAU,YAAY;AACtD,OAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,cAAc,CAAC,wBAAwB;AAC/G,YAAQ;AAAA,MACN;AAAA,IACF;AACA,6BAAyB;AAAA,EAC3B;AACA,QAAM,QAAQ;AAAA,IACZ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI,kBAAkB,IAAI;AAAA,IAC1B;AAAA,IACA;AAAA,EACF;AACA,gBAAc,KAAK;AACnB,SAAO;AACT;AACA,IAAM,aAAa,CAAC,gBAAgB;AAClC,OAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,OAAO,gBAAgB,YAAY;AAC3G,YAAQ;AAAA,MACN;AAAA,IACF;AAAA,EACF;AACA,QAAM,MAAM,OAAO,gBAAgB,aAAa,YAAY,WAAW,IAAI;AAC3E,QAAM,gBAAgB,CAAC,UAAU,eAAe,SAAS,KAAK,UAAU,UAAU;AAClF,SAAO,OAAO,eAAe,GAAG;AAChC,SAAO;AACT;AACA,IAAM,SAAS,CAAC,gBAAgB,cAAc,WAAW,WAAW,IAAI;AACxE,IAAI,QAAQ,CAAC,gBAAgB;AAC3B,OAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,cAAc;AACtE,YAAQ;AAAA,MACN;AAAA,IACF;AAAA,EACF;AACA,SAAO,OAAO,WAAW;AAC3B;", "names": ["useDebugValue", "useDebugValue", "ReactExports", "useSyncExternalStoreExports"]}