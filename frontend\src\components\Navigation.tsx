import React, { useState, useEffect } from 'react';
import { Menu, X, ShoppingBag, Heart, User } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const Navigation: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { name: 'Home', href: '#' },
    { name: 'Our Story', href: '#story' },
    { name: 'Products', href: '#products' },
    { name: 'Gifting', href: '#gifting' },
    { name: 'Contact', href: '#contact' },
  ];

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled 
        ? 'bg-cream/95 backdrop-blur-md shadow-soft' 
        : 'bg-transparent'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <motion.div 
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center"
          >
            <h1 className="font-playfair text-2xl md:text-3xl font-bold text-taupe">
              Whisk Affair
              <span className="block text-sm font-montserrat font-light text-gold tracking-widest">
                BY JINALI
              </span>
            </h1>
          </motion.div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            {navItems.map((item, index) => (
              <motion.a
                key={item.name}
                href={item.href}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="font-montserrat text-taupe hover:text-gold transition-colors duration-300 relative group"
              >
                {item.name}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gold transition-all duration-300 group-hover:w-full"></span>
              </motion.a>
            ))}
          </div>

          {/* Desktop Actions */}
          <div className="hidden lg:flex items-center space-x-4">
            <button className="p-2 text-taupe hover:text-gold transition-colors duration-300">
              <Heart className="w-5 h-5" />
            </button>
            <button className="p-2 text-taupe hover:text-gold transition-colors duration-300">
              <User className="w-5 h-5" />
            </button>
            <button className="p-2 text-taupe hover:text-gold transition-colors duration-300 relative">
              <ShoppingBag className="w-5 h-5" />
              <span className="absolute -top-1 -right-1 bg-gold text-taupe text-xs rounded-full w-4 h-4 flex items-center justify-center font-montserrat font-medium">
                2
              </span>
            </button>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="lg:hidden p-2 text-taupe hover:text-gold transition-colors duration-300"
          >
            {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="lg:hidden bg-cream/95 backdrop-blur-md border-t border-gold/20"
          >
            <div className="px-4 py-6">
              {navItems.map((item, index) => (
                <motion.a
                  key={item.name}
                  href={item.href}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="block py-3 font-montserrat text-taupe hover:text-gold transition-colors duration-300"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </motion.a>
              ))}
              <div className="flex items-center space-x-4 pt-4 border-t border-gold/20 mt-4">
                <button className="p-2 text-taupe hover:text-gold transition-colors duration-300">
                  <Heart className="w-5 h-5" />
                </button>
                <button className="p-2 text-taupe hover:text-gold transition-colors duration-300">
                  <User className="w-5 h-5" />
                </button>
                <button className="p-2 text-taupe hover:text-gold transition-colors duration-300 relative">
                  <ShoppingBag className="w-5 h-5" />
                  <span className="absolute -top-1 -right-1 bg-gold text-taupe text-xs rounded-full w-4 h-4 flex items-center justify-center font-montserrat font-medium">
                    2
                  </span>
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  );
};

export default Navigation;