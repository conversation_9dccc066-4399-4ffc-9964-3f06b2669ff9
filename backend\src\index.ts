import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import dotenv from 'dotenv';

// Import configurations and middleware
import { config } from '@/config/environment';
import { logger } from '@/config/logger';
import { connectSupabase } from '@/config/supabase';
import { connectRedis } from '@/config/redis';

// Import middleware
import { errorHandler } from '@/middleware/errorHandler';
import { rateLimiter } from '@/middleware/rateLimiter';
import { securityMiddleware } from '@/middleware/security';
import { validationMiddleware } from '@/middleware/validation';
import { authMiddleware } from '@/middleware/auth';

// Import routes
import authRoutes from '@/routes/auth';
import userRoutes from '@/routes/users';
import productRoutes from '@/routes/products';
import categoryRoutes from '@/routes/categories';
import cmsRoutes from '@/routes/cms';
import analyticsRoutes from '@/routes/analytics';
import securityRoutes from '@/routes/security';
import healthRoutes from '@/routes/health';
import adminRoutes from '@/routes/admin';

// Import socket handlers
import { setupSocketHandlers } from '@/sockets/handlers';

// Load environment variables
dotenv.config();

class WhiskAffairServer {
  private app: express.Application;
  private server: any;
  private io: SocketIOServer;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: config.cors.origin,
        methods: ['GET', 'POST'],
        credentials: true,
      },
    });

    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeErrorHandling();
    this.initializeSocketIO();
  }

  private initializeMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true,
      },
    }));

    // CORS configuration
    this.app.use(cors({
      origin: config.cors.origin,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    }));

    // Compression and parsing
    this.app.use(compression());
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Logging
    this.app.use(morgan('combined', {
      stream: {
        write: (message: string) => logger.info(message.trim()),
      },
    }));

    // Rate limiting
    this.app.use(rateLimiter);

    // Custom security middleware
    this.app.use(securityMiddleware);

    // Request validation middleware
    this.app.use(validationMiddleware);

    // Trust proxy for accurate IP addresses
    this.app.set('trust proxy', 1);
  }

  private initializeRoutes(): void {
    const apiPrefix = `/api/${config.api.version}`;

    // Health check (no auth required)
    this.app.use(`${apiPrefix}/health`, healthRoutes);

    // Authentication routes (no auth required)
    this.app.use(`${apiPrefix}/auth`, authRoutes);

    // Public and protected routes
    this.app.use(`${apiPrefix}/users`, authMiddleware, userRoutes);
    this.app.use(`${apiPrefix}/products`, productRoutes); // Some endpoints public, some protected
    this.app.use(`${apiPrefix}/categories`, categoryRoutes); // Some endpoints public, some protected
    this.app.use(`${apiPrefix}/cms`, cmsRoutes); // Some endpoints public, some protected
    this.app.use(`${apiPrefix}/analytics`, analyticsRoutes); // Mixed public/protected
    this.app.use(`${apiPrefix}/security`, authMiddleware, securityRoutes);

    // Admin panel routes (separate from API)
    this.app.use('/admin', adminRoutes);

    // API documentation (development only)
    if (config.env === 'development') {
      this.app.get(`${apiPrefix}/docs`, (_req, res) => {
        res.json({
          message: 'WhiskAffair API Documentation',
          version: config.api.version,
          endpoints: {
            auth: `${apiPrefix}/auth`,
            users: `${apiPrefix}/users`,
            products: `${apiPrefix}/products`,
            categories: `${apiPrefix}/categories`,
            cms: `${apiPrefix}/cms`,
            analytics: `${apiPrefix}/analytics`,
            security: `${apiPrefix}/security`,
            health: `${apiPrefix}/health`,
          },
        });
      });
    }

    // 404 handler for API routes
    this.app.use(`${apiPrefix}/*`, (req, res) => {
      res.status(404).json({
        success: false,
        error: 'API endpoint not found',
        path: req.path,
      });
    });

    // Root endpoint
    this.app.get('/', (_req, res) => {
      res.json({
        message: 'WhiskAffair Backend API',
        version: config.api.version,
        status: 'running',
        timestamp: new Date().toISOString(),
      });
    });
  }

  private initializeErrorHandling(): void {
    // Global error handler (must be last)
    this.app.use(errorHandler);
  }

  private initializeSocketIO(): void {
    setupSocketHandlers(this.io);
    
    this.io.on('connection', (socket) => {
      logger.info(`Socket connected: ${socket.id}`);
      
      socket.on('disconnect', () => {
        logger.info(`Socket disconnected: ${socket.id}`);
      });
    });
  }

  public async start(): Promise<void> {
    try {
      // Connect to databases
      await connectSupabase();
      await connectRedis();

      // Start server
      this.server.listen(config.port, () => {
        logger.info(`🚀 WhiskAffair Backend Server started`);
        logger.info(`📡 Server running on port ${config.port}`);
        logger.info(`🌍 Environment: ${config.env}`);
        logger.info(`📊 API Version: ${config.api.version}`);
        logger.info(`🔗 Health Check: http://localhost:${config.port}/api/${config.api.version}/health`);
        
        if (config.env === 'development') {
          logger.info(`📚 API Docs: http://localhost:${config.port}/api/${config.api.version}/docs`);
        }
      });

      // Graceful shutdown handling
      this.setupGracefulShutdown();

    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  private setupGracefulShutdown(): void {
    const gracefulShutdown = (signal: string) => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);
      
      this.server.close(() => {
        logger.info('HTTP server closed');
        
        // Close database connections
        // Add your database cleanup here
        
        logger.info('Graceful shutdown completed');
        process.exit(0);
      });

      // Force shutdown after 30 seconds
      setTimeout(() => {
        logger.error('Forced shutdown after timeout');
        process.exit(1);
      }, 30000);
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    });

    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      process.exit(1);
    });
  }
}

// Start the server
const server = new WhiskAffairServer();
server.start().catch((error) => {
  logger.error('Failed to start WhiskAffair Backend:', error);
  process.exit(1);
});

export default server;
