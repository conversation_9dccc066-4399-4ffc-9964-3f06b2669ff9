<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhiskAffair - You're Offline</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #faf7f2 0%, #f8e8e8 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #374151;
            line-height: 1.6;
        }

        .container {
            text-align: center;
            max-width: 400px;
            padding: 2rem;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1.5rem;
            background: #d4af37;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse 2s infinite;
        }

        .icon svg {
            width: 40px;
            height: 40px;
            color: white;
        }

        h1 {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }

        p {
            color: #6b7280;
            margin-bottom: 1.5rem;
        }

        .features {
            text-align: left;
            margin: 1.5rem 0;
            padding: 1rem;
            background: #f9fafb;
            border-radius: 0.5rem;
        }

        .features h3 {
            font-size: 1rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .features ul {
            list-style: none;
            padding: 0;
        }

        .features li {
            padding: 0.25rem 0;
            color: #6b7280;
            font-size: 0.875rem;
        }

        .features li::before {
            content: "✓";
            color: #d4af37;
            font-weight: bold;
            margin-right: 0.5rem;
        }

        .button {
            display: inline-block;
            background: #d4af37;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: background-color 0.2s;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }

        .button:hover {
            background: #b8941f;
        }

        .button:disabled {
            background: #d1d5db;
            cursor: not-allowed;
        }

        .status {
            margin-top: 1rem;
            padding: 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
        }

        .status.online {
            background: #d1fae5;
            color: #065f46;
        }

        .status.offline {
            background: #fee2e2;
            color: #991b1b;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        @media (max-width: 480px) {
            .container {
                margin: 1rem;
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"></path>
            </svg>
        </div>

        <h1>You're Currently Offline</h1>
        <p>Don't worry! You can still browse some content while offline.</p>

        <div class="features">
            <h3>Available Offline:</h3>
            <ul>
                <li>Previously viewed products</li>
                <li>Cached product images</li>
                <li>Your saved favorites</li>
                <li>Basic app navigation</li>
            </ul>
        </div>

        <button class="button" onclick="checkConnection()" id="retryBtn">
            Check Connection
        </button>

        <div class="status offline" id="status">
            You're currently offline. Some features may not be available.
        </div>
    </div>

    <script>
        // Check online status
        function updateStatus() {
            const status = document.getElementById('status');
            const retryBtn = document.getElementById('retryBtn');
            
            if (navigator.onLine) {
                status.textContent = 'Connection restored! You can now access all features.';
                status.className = 'status online';
                retryBtn.textContent = 'Go Online';
                retryBtn.disabled = false;
            } else {
                status.textContent = 'You\'re currently offline. Some features may not be available.';
                status.className = 'status offline';
                retryBtn.textContent = 'Check Connection';
                retryBtn.disabled = false;
            }
        }

        // Check connection function
        function checkConnection() {
            const retryBtn = document.getElementById('retryBtn');
            retryBtn.textContent = 'Checking...';
            retryBtn.disabled = true;

            // Try to fetch a small resource to test connection
            fetch('/manifest.json', { 
                method: 'HEAD',
                cache: 'no-cache'
            })
            .then(() => {
                // Connection successful, redirect to home
                window.location.href = '/';
            })
            .catch(() => {
                // Still offline
                setTimeout(() => {
                    updateStatus();
                }, 1000);
            });
        }

        // Listen for online/offline events
        window.addEventListener('online', updateStatus);
        window.addEventListener('offline', updateStatus);

        // Initial status check
        updateStatus();

        // Auto-retry when coming back online
        window.addEventListener('online', () => {
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
        });
    </script>
</body>
</html>
