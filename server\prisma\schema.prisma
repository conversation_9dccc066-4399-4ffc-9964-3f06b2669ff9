// WhiskAffair Database Schema
// This schema defines the complete data model for the e-commerce platform

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ===== USER MANAGEMENT =====

model User {
  id                String   @id @default(cuid())
  email             String   @unique
  password          String
  firstName         String
  lastName          String
  phone             String?
  avatar            String?
  role              UserRole @default(CUSTOMER)
  status            UserStatus @default(ACTIVE)
  emailVerified     Boolean  @default(false)
  emailVerifiedAt   DateTime?
  twoFactorEnabled  Boolean  @default(false)
  twoFactorSecret   String?
  lastLoginAt       DateTime?
  lastLoginIp       String?
  loginAttempts     Int      @default(0)
  lockedUntil       DateTime?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relationships
  addresses         Address[]
  orders            Order[]
  reviews           Review[]
  wishlistItems     WishlistItem[]
  cartItems         CartItem[]
  securityLogs      SecurityLog[]
  refreshTokens     RefreshToken[]
  
  @@map("users")
}

model RefreshToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())
  
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("refresh_tokens")
}

model Address {
  id          String  @id @default(cuid())
  userId      String
  firstName   String
  lastName    String
  company     String?
  address1    String
  address2    String?
  city        String
  province    String
  country     String
  postalCode  String
  phone       String?
  isDefault   Boolean @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user        User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  orders      Order[]
  
  @@map("addresses")
}

// ===== PRODUCT MANAGEMENT =====

model Category {
  id          String    @id @default(cuid())
  name        String
  slug        String    @unique
  description String?
  image       String?
  parentId    String?
  sortOrder   Int       @default(0)
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  parent      Category? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    Category[] @relation("CategoryHierarchy")
  products    Product[]
  
  @@map("categories")
}

model Product {
  id              String        @id @default(cuid())
  name            String
  slug            String        @unique
  description     String
  shortDescription String?
  price           Decimal       @db.Decimal(10, 2)
  compareAtPrice  Decimal?      @db.Decimal(10, 2)
  sku             String        @unique
  barcode         String?
  weight          Decimal?      @db.Decimal(8, 2)
  dimensions      Json?         // {length, width, height}
  categoryId      String
  status          ProductStatus @default(DRAFT)
  featured        Boolean       @default(false)
  tags            String[]
  metaTitle       String?
  metaDescription String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  category        Category      @relation(fields: [categoryId], references: [id])
  images          ProductImage[]
  variants        ProductVariant[]
  inventory       ProductInventory?
  reviews         Review[]
  orderItems      OrderItem[]
  cartItems       CartItem[]
  wishlistItems   WishlistItem[]
  analyticsEvents AnalyticsEvent[]
  
  @@map("products")
}

model ProductImage {
  id        String  @id @default(cuid())
  productId String
  url       String
  altText   String?
  sortOrder Int     @default(0)
  isMain    Boolean @default(false)
  createdAt DateTime @default(now())

  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  
  @@map("product_images")
}

model ProductVariant {
  id        String  @id @default(cuid())
  productId String
  name      String
  sku       String  @unique
  price     Decimal @db.Decimal(10, 2)
  inventory Int     @default(0)
  attributes Json   // {size: "Large", color: "Red", etc.}
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  orderItems OrderItem[]
  cartItems  CartItem[]
  
  @@map("product_variants")
}

model ProductInventory {
  id                String  @id @default(cuid())
  productId         String  @unique
  quantity          Int     @default(0)
  lowStockThreshold Int     @default(10)
  trackQuantity     Boolean @default(true)
  allowBackorder    Boolean @default(false)
  updatedAt         DateTime @updatedAt

  product           Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  
  @@map("product_inventory")
}

// ===== ORDER MANAGEMENT =====

model Order {
  id                String           @id @default(cuid())
  orderNumber       String           @unique
  userId            String?
  email             String
  status            OrderStatus      @default(PENDING)
  paymentStatus     PaymentStatus    @default(PENDING)
  fulfillmentStatus FulfillmentStatus @default(UNFULFILLED)
  subtotal          Decimal          @db.Decimal(10, 2)
  tax               Decimal          @db.Decimal(10, 2) @default(0)
  shipping          Decimal          @db.Decimal(10, 2) @default(0)
  discount          Decimal          @db.Decimal(10, 2) @default(0)
  total             Decimal          @db.Decimal(10, 2)
  currency          String           @default("USD")
  shippingAddressId String
  billingAddressId  String?
  notes             String?
  trackingNumber    String?
  shippedAt         DateTime?
  deliveredAt       DateTime?
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt

  user              User?            @relation(fields: [userId], references: [id])
  shippingAddress   Address          @relation(fields: [shippingAddressId], references: [id])
  items             OrderItem[]
  payments          Payment[]
  
  @@map("orders")
}

model OrderItem {
  id          String  @id @default(cuid())
  orderId     String
  productId   String
  variantId   String?
  quantity    Int
  price       Decimal @db.Decimal(10, 2)
  title       String
  image       String?
  createdAt   DateTime @default(now())

  order       Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product     Product @relation(fields: [productId], references: [id])
  variant     ProductVariant? @relation(fields: [variantId], references: [id])
  
  @@map("order_items")
}

model Payment {
  id              String        @id @default(cuid())
  orderId         String
  amount          Decimal       @db.Decimal(10, 2)
  currency        String        @default("USD")
  status          PaymentStatus @default(PENDING)
  method          String        // stripe, paypal, etc.
  transactionId   String?
  gatewayResponse Json?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  order           Order         @relation(fields: [orderId], references: [id], onDelete: Cascade)
  
  @@map("payments")
}

// ===== SHOPPING CART & WISHLIST =====

model CartItem {
  id        String  @id @default(cuid())
  userId    String
  productId String
  variantId String?
  quantity  Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  variant   ProductVariant? @relation(fields: [variantId], references: [id], onDelete: Cascade)
  
  @@unique([userId, productId, variantId])
  @@map("cart_items")
}

model WishlistItem {
  id        String   @id @default(cuid())
  userId    String
  productId String
  createdAt DateTime @default(now())

  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  
  @@unique([userId, productId])
  @@map("wishlist_items")
}

// ===== REVIEWS & RATINGS =====

model Review {
  id        String   @id @default(cuid())
  userId    String
  productId String
  rating    Int      // 1-5 stars
  title     String?
  content   String
  verified  Boolean  @default(false) // Verified purchase
  helpful   Int      @default(0)
  reported  Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  
  @@unique([userId, productId])
  @@map("reviews")
}

// ===== ANALYTICS & TRACKING =====

model AnalyticsEvent {
  id         String   @id @default(cuid())
  eventType  String
  userId     String?
  sessionId  String
  productId  String?
  properties Json
  page       String?
  referrer   String?
  userAgent  String?
  ipAddress  String?
  createdAt  DateTime @default(now())

  product    Product? @relation(fields: [productId], references: [id])
  
  @@index([eventType, createdAt])
  @@index([userId, createdAt])
  @@index([sessionId, createdAt])
  @@map("analytics_events")
}

// ===== SECURITY & AUDIT =====

model SecurityLog {
  id        String   @id @default(cuid())
  userId    String?
  event     String
  details   Json
  ipAddress String?
  userAgent String?
  success   Boolean  @default(true)
  createdAt DateTime @default(now())

  user      User?    @relation(fields: [userId], references: [id])
  
  @@index([userId, createdAt])
  @@index([event, createdAt])
  @@map("security_logs")
}

// ===== CONTENT MANAGEMENT =====

model Page {
  id          String      @id @default(cuid())
  title       String
  slug        String      @unique
  content     String
  excerpt     String?
  featuredImage String?
  status      ContentStatus @default(DRAFT)
  metaTitle   String?
  metaDescription String?
  publishedAt DateTime?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  
  @@map("pages")
}

model BlogPost {
  id          String      @id @default(cuid())
  title       String
  slug        String      @unique
  content     String
  excerpt     String
  featuredImage String?
  author      String
  tags        String[]
  categories  String[]
  status      ContentStatus @default(DRAFT)
  metaTitle   String?
  metaDescription String?
  publishedAt DateTime?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  
  @@map("blog_posts")
}

// ===== ENUMS =====

enum UserRole {
  SUPER_ADMIN
  PRODUCT_MANAGER
  CONTENT_EDITOR
  CUSTOMER
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  DELETED
}

enum ProductStatus {
  ACTIVE
  DRAFT
  ARCHIVED
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
  PARTIALLY_REFUNDED
}

enum FulfillmentStatus {
  UNFULFILLED
  PARTIAL
  FULFILLED
}

enum ContentStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}
