import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Heart, ShoppingBag, Eye, Star, Gift } from 'lucide-react';

const ProductShowcase: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [hoveredProduct, setHoveredProduct] = useState<number | null>(null);

  const categories = [
    { id: 'all', name: 'All Products' },
    { id: 'cookies', name: 'Artisan Cookies' },
    { id: 'cakes', name: 'Celebration Cakes' },
    { id: 'pastries', name: 'French Pastries' },
    { id: 'seasonal', name: 'Seasonal Specials' },
  ];

  const products = [
    {
      id: 1,
      name: 'Classic Chocolate Chip',
      category: 'cookies',
      price: '₹450',
      originalPrice: '₹500',
      rating: 4.9,
      reviews: 127,
      image: 'https://images.pexels.com/photos/230325/pexels-photo-230325.jpeg?auto=compress&cs=tinysrgb&w=400',
      badge: 'Bestseller',
      description: 'Rich, buttery-smooth cookies with premium dark chocolate chips',
    },
    {
      id: 2,
      name: 'Vanilla Bean Celebration Cake',
      category: 'cakes',
      price: '₹1,200',
      rating: 5.0,
      reviews: 89,
      image: 'https://images.pexels.com/photos/1070850/pexels-photo-1070850.jpeg?auto=compress&cs=tinysrgb&w=400',
      badge: 'Premium',
      description: 'Elegant vanilla bean cake with cream cheese frosting',
    },
    {
      id: 3,
      name: 'French Macarons Collection',
      category: 'pastries',
      price: '₹800',
      rating: 4.8,
      reviews: 156,
      image: 'https://images.pexels.com/photos/3067763/pexels-photo-3067763.jpeg?auto=compress&cs=tinysrgb&w=400',
      badge: 'Limited',
      description: 'Delicate almond macarons in six exquisite flavors',
    },
    {
      id: 4,
      name: 'Festive Gingerbread Collection',
      category: 'seasonal',
      price: '₹650',
      rating: 4.7,
      reviews: 92,
      image: 'https://images.pexels.com/photos/1031673/pexels-photo-1031673.jpeg?auto=compress&cs=tinysrgb&w=400',
      badge: 'Seasonal',
      description: 'Warm spiced gingerbread cookies perfect for celebrations',
    },
    {
      id: 5,
      name: 'Strawberry Shortcake',
      category: 'cakes',
      price: '₹950',
      rating: 4.9,
      reviews: 74,
      image: 'https://images.pexels.com/photos/376464/pexels-photo-376464.jpeg?auto=compress&cs=tinysrgb&w=400',
      badge: 'Fresh',
      description: 'Light sponge cake with fresh strawberries and cream',
    },
    {
      id: 6,
      name: 'Artisan Oatmeal Raisin',
      category: 'cookies',
      price: '₹400',
      rating: 4.6,
      reviews: 203,
      image: 'https://images.pexels.com/photos/1028714/pexels-photo-1028714.jpeg?auto=compress&cs=tinysrgb&w=400',
      badge: 'Healthy',
      description: 'Wholesome oats with plump raisins and warm spices',
    },
  ];

  const filteredProducts = selectedCategory === 'all' 
    ? products 
    : products.filter(product => product.category === selectedCategory);

  const getBadgeColor = (badge: string) => {
    switch (badge) {
      case 'Bestseller': return 'bg-gold text-taupe';
      case 'Premium': return 'bg-taupe text-cream';
      case 'Limited': return 'bg-red-500 text-white';
      case 'Seasonal': return 'bg-orange-500 text-white';
      case 'Fresh': return 'bg-green-500 text-white';
      case 'Healthy': return 'bg-blue-500 text-white';
      default: return 'bg-gold text-taupe';
    }
  };

  return (
    <section id="products" className="py-20 bg-cream">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="font-playfair text-4xl md:text-5xl font-bold text-taupe mb-6">
            Our Signature
            <span className="block text-gold">Collections</span>
          </h2>
          <p className="font-montserrat text-lg text-taupe/80 max-w-2xl mx-auto">
            Each creation is meticulously crafted with premium ingredients, 
            delivering uncompromising taste without the butter.
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-6 py-3 rounded-full font-montserrat font-medium transition-all duration-300 ${
                selectedCategory === category.id
                  ? 'bg-gold text-taupe shadow-gold'
                  : 'bg-white text-taupe hover:bg-gold/20 border border-gold/30'
              }`}
            >
              {category.name}
            </button>
          ))}
        </motion.div>

        {/* Products Grid */}
        <AnimatePresence mode="wait">
          <motion.div
            key={selectedCategory}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.4 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {filteredProducts.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="group relative bg-white rounded-3xl shadow-soft overflow-hidden hover:shadow-gold transition-all duration-500"
                onMouseEnter={() => setHoveredProduct(product.id)}
                onMouseLeave={() => setHoveredProduct(null)}
              >
                {/* Product Image */}
                <div className="relative h-64 overflow-hidden">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300"></div>
                  
                  {/* Badge */}
                  <div className={`absolute top-4 left-4 px-3 py-1 rounded-full text-xs font-montserrat font-semibold ${getBadgeColor(product.badge)}`}>
                    {product.badge}
                  </div>

                  {/* Hover Actions */}
                  <AnimatePresence>
                    {hoveredProduct === product.id && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                        className="absolute inset-0 flex items-center justify-center space-x-4"
                      >
                        <button className="w-12 h-12 bg-white/90 rounded-full flex items-center justify-center shadow-soft hover:bg-white transition-all duration-300">
                          <Eye className="w-5 h-5 text-taupe" />
                        </button>
                        <button className="w-12 h-12 bg-white/90 rounded-full flex items-center justify-center shadow-soft hover:bg-white transition-all duration-300">
                          <Heart className="w-5 h-5 text-taupe" />
                        </button>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                {/* Product Info */}
                <div className="p-6">
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="font-playfair text-xl font-semibold text-taupe group-hover:text-gold transition-colors duration-300">
                      {product.name}
                    </h3>
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-gold fill-current" />
                      <span className="font-montserrat text-sm text-taupe/70">
                        {product.rating}
                      </span>
                    </div>
                  </div>

                  <p className="font-montserrat text-sm text-taupe/60 mb-4 leading-relaxed">
                    {product.description}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="font-montserrat font-bold text-lg text-taupe">
                        {product.price}
                      </span>
                      {product.originalPrice && (
                        <span className="font-montserrat text-sm text-taupe/40 line-through">
                          {product.originalPrice}
                        </span>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <button className="w-10 h-10 bg-gold/20 rounded-full flex items-center justify-center hover:bg-gold hover:text-taupe transition-all duration-300">
                        <Gift className="w-4 h-4" />
                      </button>
                      <button className="px-4 py-2 bg-gold text-taupe rounded-full font-montserrat font-medium hover:shadow-gold transition-all duration-300 flex items-center space-x-2">
                        <ShoppingBag className="w-4 h-4" />
                        <span>Add</span>
                      </button>
                    </div>
                  </div>

                  <div className="mt-3 font-montserrat text-xs text-taupe/50">
                    {product.reviews} reviews
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>

        {/* View All Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <button className="px-8 py-4 bg-taupe text-cream rounded-full font-montserrat font-semibold hover:bg-taupe/90 transition-all duration-300">
            View Complete Collection
          </button>
        </motion.div>
      </div>
    </section>
  );
};

export default ProductShowcase;