import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import FrontendApp from './frontend/FrontendApp';
import { useAuthStore } from './store/authStore';
import LoginPage from './pages/auth/LoginPage';
import TwoFactorAuth from './pages/auth/TwoFactorAuth';
import DashboardLayout from './components/layout/DashboardLayout';
import Dashboard from './pages/Dashboard';
import ProductManagement from './pages/products/ProductManagement';
import OrderManagement from './pages/orders/OrderManagement';
import UserManagement from './pages/users/UserManagement';
import ContentManagement from './pages/content/ContentManagement';
import SystemSettings from './pages/settings/SystemSettings';
import ProtectedRoute from './components/auth/ProtectedRoute';

function App() {
  const { isAuthenticated, user } = useAuthStore();

  return (
    <Routes>
      {/* Frontend E-commerce Site - Default Route */}
      <Route path="/*" element={<FrontendApp />} />
      
      {/* Admin Panel Routes */}
      <Route path="/admin/*" element={<AdminApp />} />
    </Routes>
  );
}

// Admin App Component
const AdminApp: React.FC = () => {
  const { isAuthenticated, user } = useAuthStore();

  // Loading state
  if (user === undefined) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <Routes>
        <Route path="/login" element={<LoginPage />} />
        <Route path="/2fa" element={<TwoFactorAuth />} />
        <Route path="*" element={<Navigate to="/admin/login" replace />} />
      </Routes>
    );
  }

  return (
    <DashboardLayout>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route 
          path="/products/*" 
          element={
            <ProtectedRoute requiredPermissions={['products.read']}>
              <ProductManagement />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/orders/*" 
          element={
            <ProtectedRoute requiredPermissions={['orders.read']}>
              <OrderManagement />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/users/*" 
          element={
            <ProtectedRoute requiredPermissions={['users.read']}>
              <UserManagement />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/content/*" 
          element={
            <ProtectedRoute requiredPermissions={['content.read']}>
              <ContentManagement />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/settings/*" 
          element={
            <ProtectedRoute requiredPermissions={['system.read']}>
              <SystemSettings />
            </ProtectedRoute>
          } 
        />
        <Route path="*" element={<Navigate to="/admin/dashboard" replace />} />
      </Routes>
    </DashboardLayout>
  );
};

export default App;