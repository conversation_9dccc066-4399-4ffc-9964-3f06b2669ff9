# 🔧 WhiskAffair Environment Setup Guide

## 📋 **Overview**

This guide explains how to set up environment variables for the WhiskAffair project using `.env.local` files.

## 🏗️ **Environment File Structure**

```
WhiskAffair/
├── .env.local              # Frontend environment variables
├── .env.example            # Frontend example file
├── server/
│   ├── .env.local          # Backend environment variables  
│   └── .env.example        # Backend example file
└── .gitignore              # Excludes .env.local files
```

## ⚙️ **Setup Instructions**

### **1. Backend Environment Setup**

```bash
# Navigate to server directory
cd server

# Copy example file (if needed)
cp .env.example .env.local

# Edit with your actual values
# The .env.local file is already configured with your Supabase credentials
```

### **2. Frontend Environment Setup**

```bash
# Navigate to project root
cd ..

# Copy example file (if needed)  
cp .env.example .env.local

# Edit with your actual values
# The .env.local file is already configured with your Supabase credentials
```

## 🔑 **Required Variables**

### **Backend (.env.local)**
- ✅ **SUPABASE_URL** - Your Supabase project URL
- ✅ **SUPABASE_ANON_KEY** - Supabase anonymous key
- ✅ **SUPABASE_SERVICE_ROLE_KEY** - Supabase service role key
- ✅ **JWT_SECRET** - JWT signing secret (32+ characters)
- ✅ **JWT_REFRESH_SECRET** - JWT refresh secret (32+ characters)
- ✅ **SESSION_SECRET** - Session signing secret
- ✅ **REDIS_URL** - Redis connection URL

### **Frontend (.env.local)**
- ✅ **VITE_SUPABASE_URL** - Your Supabase project URL
- ✅ **VITE_SUPABASE_ANON_KEY** - Supabase anonymous key
- ✅ **VITE_API_BASE_URL** - Backend API URL

## 🔒 **Security Notes**

1. **Never commit `.env.local` files** - They contain sensitive credentials
2. **Use `.env.example` files** - For sharing configuration templates
3. **Keep JWT secrets secure** - Use 32+ character random strings
4. **Rotate credentials regularly** - Especially in production

## 🚀 **Development Workflow**

```bash
# 1. Clone repository
git clone <repository-url>

# 2. Install dependencies
npm install
cd server && npm install

# 3. Set up environment files
# Backend: server/.env.local (already configured)
# Frontend: .env.local (already configured)

# 4. Start development servers
npm run dev          # Frontend
cd server && npm run dev  # Backend
```

## 🔄 **Migration Status**

- ✅ **Environment files** - Migrated to `.env.local`
- ✅ **Supabase integration** - Fully configured
- 🔄 **JWT authentication** - Keeping for gradual migration
- ⏳ **Complete Supabase Auth** - Future migration step

## 🆘 **Troubleshooting**

### **Missing Environment Variables**
```bash
# Check if .env.local files exist
ls -la .env.local
ls -la server/.env.local

# Copy from examples if missing
cp .env.example .env.local
cp server/.env.example server/.env.local
```

### **Invalid Supabase Credentials**
- Verify your Supabase project URL and keys
- Check Supabase dashboard for correct values
- Ensure service role key has proper permissions

### **JWT Errors**
- Ensure JWT secrets are 32+ characters
- Use different secrets for JWT_SECRET and JWT_REFRESH_SECRET
- Generate secure random strings for production
