import { PrismaClient } from '@prisma/client';
import { logger } from './logger';
import { config } from './environment';

// Extend PrismaClient with custom methods
class ExtendedPrismaClient extends PrismaClient {
  constructor() {
    super({
      log: [
        {
          emit: 'event',
          level: 'query',
        },
        {
          emit: 'event',
          level: 'error',
        },
        {
          emit: 'event',
          level: 'info',
        },
        {
          emit: 'event',
          level: 'warn',
        },
      ],
      errorFormat: 'pretty',
    });

    // Log database queries in development
    if (config.env === 'development') {
      this.$on('query', (e) => {
        logger.debug(`Query: ${e.query}`);
        logger.debug(`Params: ${e.params}`);
        logger.debug(`Duration: ${e.duration}ms`);
      });
    }

    // Log database errors
    this.$on('error', (e) => {
      logger.error('Database error:', e);
    });

    // Log database info
    this.$on('info', (e) => {
      logger.info('Database info:', e.message);
    });

    // Log database warnings
    this.$on('warn', (e) => {
      logger.warn('Database warning:', e.message);
    });
  }

  // Custom method to safely disconnect
  async safeDisconnect(): Promise<void> {
    try {
      await this.$disconnect();
      logger.info('Database disconnected successfully');
    } catch (error) {
      logger.error('Error disconnecting from database:', error);
    }
  }

  // Health check method
  async healthCheck(): Promise<boolean> {
    try {
      await this.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      logger.error('Database health check failed:', error);
      return false;
    }
  }

  // Transaction wrapper with retry logic
  async withRetry<T>(
    operation: (prisma: PrismaClient) => Promise<T>,
    maxRetries: number = 3
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation(this);
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === maxRetries) {
          logger.error(`Database operation failed after ${maxRetries} attempts:`, error);
          throw error;
        }

        // Wait before retry (exponential backoff)
        const delay = Math.pow(2, attempt) * 1000;
        logger.warn(`Database operation failed (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms:`, error);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  // Bulk operations helper
  async bulkUpsert<T>(
    model: any,
    data: T[],
    uniqueFields: string[]
  ): Promise<void> {
    const batchSize = 100;
    
    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize);
      
      await this.$transaction(
        batch.map((item) =>
          model.upsert({
            where: uniqueFields.reduce((acc, field) => {
              acc[field] = (item as any)[field];
              return acc;
            }, {} as any),
            update: item,
            create: item,
          })
        )
      );
    }
  }

  // Analytics helper methods
  async getAnalyticsData(
    startDate: Date,
    endDate: Date,
    eventTypes?: string[]
  ) {
    const whereClause: any = {
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    };

    if (eventTypes && eventTypes.length > 0) {
      whereClause.eventType = {
        in: eventTypes,
      };
    }

    return await this.analyticsEvent.findMany({
      where: whereClause,
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async getProductAnalytics(productId: string, days: number = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const events = await this.analyticsEvent.findMany({
      where: {
        productId,
        createdAt: {
          gte: startDate,
        },
      },
    });

    const analytics = {
      views: events.filter(e => e.eventType === 'product_view').length,
      clicks: events.filter(e => e.eventType === 'product_click').length,
      addToCarts: events.filter(e => e.eventType === 'add_to_cart').length,
      purchases: events.filter(e => e.eventType === 'purchase').length,
    };

    return {
      ...analytics,
      conversionRate: analytics.views > 0 ? analytics.purchases / analytics.views : 0,
    };
  }

  // Security helper methods
  async logSecurityEvent(
    event: string,
    details: any,
    userId?: string,
    ipAddress?: string,
    userAgent?: string,
    success: boolean = true
  ) {
    return await this.securityLog.create({
      data: {
        event,
        details,
        userId,
        ipAddress,
        userAgent,
        success,
      },
    });
  }

  async getFailedLoginAttempts(email: string, timeWindow: number = 15): Promise<number> {
    const since = new Date(Date.now() - timeWindow * 60 * 1000);
    
    const user = await this.user.findUnique({
      where: { email },
    });

    if (!user) return 0;

    const attempts = await this.securityLog.count({
      where: {
        userId: user.id,
        event: 'login_failed',
        createdAt: {
          gte: since,
        },
      },
    });

    return attempts;
  }

  // Performance monitoring
  async getSlowQueries(thresholdMs: number = 1000) {
    // This would require custom query logging implementation
    // For now, return empty array
    return [];
  }
}

// Create global database instance
export const db = new ExtendedPrismaClient();

// Connection management
export const connectDatabase = async (): Promise<void> => {
  try {
    await db.$connect();
    logger.info('✅ Database connected successfully');
    
    // Run health check
    const isHealthy = await db.healthCheck();
    if (!isHealthy) {
      throw new Error('Database health check failed');
    }
    
    logger.info('✅ Database health check passed');
  } catch (error) {
    logger.error('❌ Failed to connect to database:', error);
    throw error;
  }
};

export const disconnectDatabase = async (): Promise<void> => {
  await db.safeDisconnect();
};

// Graceful shutdown handler
process.on('beforeExit', async () => {
  await disconnectDatabase();
});

process.on('SIGINT', async () => {
  await disconnectDatabase();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await disconnectDatabase();
  process.exit(0);
});

export default db;
