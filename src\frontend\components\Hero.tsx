import React from 'react';
import { motion } from 'framer-motion';
import { ArrowRight, Play } from 'lucide-react';

const Hero: React.FC = () => {
  return (
    <section className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* Background Video Placeholder */}
      <div className="absolute inset-0 bg-gradient-to-br from-blush via-cream to-gold-light">
        <div className="absolute inset-0 bg-black/10"></div>
        {/* Animated background elements */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-gold/20 rounded-full animate-float"></div>
        <div className="absolute bottom-32 right-16 w-24 h-24 bg-blush/30 rounded-full animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-1/2 right-1/4 w-16 h-16 bg-gold/15 rounded-full animate-float" style={{ animationDelay: '4s' }}></div>
      </div>

      {/* Video Play Button */}
      <motion.button
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.5 }}
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20 bg-white/90 rounded-full flex items-center justify-center shadow-gold hover:bg-white transition-all duration-300 group z-10"
      >
        <Play className="w-8 h-8 text-taupe ml-1 group-hover:scale-110 transition-transform" />
      </motion.button>

      {/* Content */}
      <div className="relative z-20 text-center px-4 max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <h1 className="font-playfair text-4xl md:text-6xl lg:text-7xl font-bold text-taupe mb-6 leading-tight">
            Butter-Free.
            <span className="block text-gold">Guilt-Free.</span>
            <span className="block text-3xl md:text-4xl lg:text-5xl mt-2">
              Luxuriously Indulgent.
            </span>
          </h1>
        </motion.div>

        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="font-montserrat text-lg md:text-xl text-taupe/80 mb-8 max-w-2xl mx-auto leading-relaxed"
        >
          Discover Mumbai's finest collection of artisanal butter-free cookies and desserts, 
          crafted with love and the finest ingredients for the discerning palate.
        </motion.p>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="flex flex-col sm:flex-row gap-4 justify-center items-center"
        >
          <a 
            href="/products"
            className="group bg-gradient-gold text-taupe px-8 py-4 rounded-full font-montserrat font-semibold hover:shadow-gold transition-all duration-300 flex items-center space-x-2"
          >
            <span>Shop Now</span>
            <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
          </a>
          
          <a 
            href="/products#gifting"
            className="font-montserrat text-taupe border-2 border-taupe px-8 py-4 rounded-full hover:bg-taupe hover:text-cream transition-all duration-300"
          >
            View Gift Collections
          </a>
        </motion.div>

        {/* Trust indicators */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mt-12 flex flex-wrap justify-center items-center gap-8 text-sm font-montserrat text-taupe/60"
        >
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-gold rounded-full"></div>
            <span>100% Butter-Free</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-gold rounded-full"></div>
            <span>Premium Ingredients</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-gold rounded-full"></div>
            <span>Handcrafted in Mumbai</span>
          </div>
        </motion.div>
      </div>

      {/* Scroll indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <div className="w-px h-12 bg-taupe/30 relative">
          <div className="absolute top-0 w-px h-6 bg-gold animate-pulse"></div>
        </div>
      </motion.div>
    </section>
  );
};

export default Hero;