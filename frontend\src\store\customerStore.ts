import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Simple Customer Interface for Consumer Frontend
export interface Customer {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
}

// Simple Cart Item Interface
export interface CartItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  quantity: number;
  image?: string;
  variant?: string;
}

interface CustomerState {
  // Customer auth (simple)
  customer: Customer | null;
  isAuthenticated: boolean;
  isLoading: boolean;

  // Shopping cart
  cart: CartItem[];
  cartTotal: number;

  // Customer actions
  login: (email: string, password: string) => Promise<void>;
  register: (customerData: Omit<Customer, 'id'> & { password: string }) => Promise<void>;
  logout: () => void;
  setCustomer: (customer: Customer) => void;

  // Cart actions
  addToCart: (item: Omit<CartItem, 'id'>) => void;
  removeFromCart: (itemId: string) => void;
  updateCartQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  calculateTotal: () => void;
}

export const useCustomerStore = create<CustomerState>()(
  persist(
    (set, get) => ({
      // Initial state
      customer: null,
      isAuthenticated: false,
      isLoading: false,
      cart: [],
      cartTotal: 0,

      // Customer authentication (simplified)
      login: async (email: string, password: string) => {
        set({ isLoading: true });

        try {
          // TODO: Replace with actual API call to backend
          const response = await fetch('/api/v1/customers/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email, password }),
          });

          if (!response.ok) throw new Error('Login failed');

          const { customer, token } = await response.json();

          // Store token in localStorage for API calls
          localStorage.setItem('customerToken', token);

          set({
            customer,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      register: async (customerData) => {
        set({ isLoading: true });

        try {
          // TODO: Replace with actual API call to backend
          const response = await fetch('/api/v1/customers/register', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(customerData),
          });

          if (!response.ok) throw new Error('Registration failed');

          const { customer, token } = await response.json();

          localStorage.setItem('customerToken', token);

          set({
            customer,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      logout: () => {
        localStorage.removeItem('customerToken');
        set({
          customer: null,
          isAuthenticated: false,
          cart: [],
          cartTotal: 0,
        });
      },

      setCustomer: (customer) => {
        set({ customer, isAuthenticated: true });
      },

      // Cart management
      addToCart: (item) => {
        const { cart } = get();
        const existingItem = cart.find(cartItem =>
          cartItem.productId === item.productId && cartItem.variant === item.variant
        );

        if (existingItem) {
          // Update quantity if item already exists
          get().updateCartQuantity(existingItem.id, existingItem.quantity + item.quantity);
        } else {
          // Add new item
          const newItem: CartItem = {
            ...item,
            id: `cart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          };

          set({ cart: [...cart, newItem] });
          get().calculateTotal();
        }
      },

      removeFromCart: (itemId) => {
        const { cart } = get();
        set({ cart: cart.filter(item => item.id !== itemId) });
        get().calculateTotal();
      },

      updateCartQuantity: (itemId, quantity) => {
        const { cart } = get();
        if (quantity <= 0) {
          get().removeFromCart(itemId);
          return;
        }

        set({
          cart: cart.map(item =>
            item.id === itemId ? { ...item, quantity } : item
          ),
        });
        get().calculateTotal();
      },

      clearCart: () => {
        set({ cart: [], cartTotal: 0 });
      },

      calculateTotal: () => {
        const { cart } = get();
        const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        set({ cartTotal: total });
      },
    }),
    {
      name: 'whisk-affair-customer',
      partialize: (state) => ({
        customer: state.customer,
        isAuthenticated: state.isAuthenticated,
        cart: state.cart,
        cartTotal: state.cartTotal,
      }),
    }
  )
);