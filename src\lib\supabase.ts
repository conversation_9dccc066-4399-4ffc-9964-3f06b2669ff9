import { createClient } from '@supabase/supabase-js';

// Supabase Client Configuration for WhiskAffair Frontend
// Professional setup with type safety and error handling

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

// Create Supabase client with optimized configuration
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
  },
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
  global: {
    headers: {
      'X-Client-Info': 'whisk-affair-frontend',
    },
  },
});

// Auth helper functions
export const auth = {
  // Sign up
  async signUp(email: string, password: string, userData: any) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: userData,
      },
    });
    
    if (error) throw error;
    return data;
  },

  // Sign in
  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) throw error;
    return data;
  },

  // Sign out
  async signOut() {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  },

  // Get current user
  async getCurrentUser() {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw error;
    return user;
  },

  // Get current session
  async getCurrentSession() {
    const { data: { session }, error } = await supabase.auth.getSession();
    if (error) throw error;
    return session;
  },

  // Reset password
  async resetPassword(email: string) {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });
    
    if (error) throw error;
  },

  // Update password
  async updatePassword(password: string) {
    const { error } = await supabase.auth.updateUser({ password });
    if (error) throw error;
  },

  // Listen to auth changes
  onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback);
  },
};

// Database helper functions
export const db = {
  // Generic select
  async select(table: string, columns = '*', filters?: Record<string, any>) {
    let query = supabase.from(table).select(columns);
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        query = query.eq(key, value);
      });
    }
    
    const { data, error } = await query;
    if (error) throw error;
    return data;
  },

  // Generic insert
  async insert(table: string, data: any) {
    const { data: result, error } = await supabase
      .from(table)
      .insert(data)
      .select();
    
    if (error) throw error;
    return result;
  },

  // Generic update
  async update(table: string, id: string, data: any) {
    const { data: result, error } = await supabase
      .from(table)
      .update(data)
      .eq('id', id)
      .select();
    
    if (error) throw error;
    return result;
  },

  // Generic delete
  async delete(table: string, id: string) {
    const { error } = await supabase
      .from(table)
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  },

  // Get user profile
  async getUserProfile(userId: string) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (error) throw error;
    return data;
  },

  // Update user profile
  async updateUserProfile(userId: string, updates: any) {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },
};

// Analytics helper functions
export const analytics = {
  // Track event
  async trackEvent(eventData: {
    event_type: string;
    session_id: string;
    user_id?: string;
    product_id?: string;
    properties?: any;
    page?: string;
    referrer?: string;
  }) {
    const { error } = await supabase
      .from('analytics_events')
      .insert({
        ...eventData,
        user_agent: navigator.userAgent,
        created_at: new Date().toISOString(),
      });
    
    if (error) {
      console.warn('Failed to track analytics event:', error);
    }
  },

  // Track page view
  async trackPageView(page: string, userId?: string) {
    await this.trackEvent({
      event_type: 'page_view',
      session_id: this.getSessionId(),
      user_id: userId,
      page,
      referrer: document.referrer,
      properties: {
        title: document.title,
        url: window.location.href,
      },
    });
  },

  // Track product view
  async trackProductView(productId: string, userId?: string) {
    await this.trackEvent({
      event_type: 'product_view',
      session_id: this.getSessionId(),
      user_id: userId,
      product_id: productId,
      page: window.location.pathname,
      properties: {
        timestamp: Date.now(),
      },
    });
  },

  // Get or create session ID
  getSessionId(): string {
    let sessionId = sessionStorage.getItem('analytics_session_id');
    if (!sessionId) {
      sessionId = crypto.randomUUID();
      sessionStorage.setItem('analytics_session_id', sessionId);
    }
    return sessionId;
  },
};

// Real-time subscriptions
export const realtime = {
  // Subscribe to table changes
  subscribeToTable(
    table: string,
    callback: (payload: any) => void,
    filter?: string
  ) {
    return supabase
      .channel(`${table}-changes`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table,
          filter,
        },
        callback
      )
      .subscribe();
  },

  // Subscribe to analytics events
  subscribeToAnalytics(callback: (payload: any) => void) {
    return this.subscribeToTable('analytics_events', callback);
  },

  // Subscribe to orders
  subscribeToOrders(userId: string, callback: (payload: any) => void) {
    return this.subscribeToTable('orders', callback, `user_id=eq.${userId}`);
  },

  // Unsubscribe from channel
  unsubscribe(subscription: any) {
    return supabase.removeChannel(subscription);
  },
};

// File storage helpers
export const storage = {
  // Upload file
  async uploadFile(bucket: string, path: string, file: File) {
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, file);
    
    if (error) throw error;
    return data;
  },

  // Get file URL
  getFileUrl(bucket: string, path: string) {
    const { data } = supabase.storage
      .from(bucket)
      .getPublicUrl(path);
    
    return data.publicUrl;
  },

  // Delete file
  async deleteFile(bucket: string, path: string) {
    const { error } = await supabase.storage
      .from(bucket)
      .remove([path]);
    
    if (error) throw error;
  },
};

export default supabase;
