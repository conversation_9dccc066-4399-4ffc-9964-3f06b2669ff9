# WhiskAffair Backend Environment Configuration

# Server Configuration
NODE_ENV=development
PORT=3001
API_VERSION=v1
CORS_ORIGIN=http://localhost:3000

# Supabase Configuration
SUPABASE_URL=https://ibocdnngycylvgktbgzw.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlib2Nkbm5neWN5bHZna3RiZ3p3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njk2MTUsImV4cCI6MjA2NzM0NTYxNX0.E4QcJqovnt0l1lUS3hL0KTnEGBCtoLGt8Ym06GgASUY
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlib2Nkbm5neWN5bHZna3RiZ3p3Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTc2OTYxNSwiZXhwIjoyMDY3MzQ1NjE1fQ.XFGiO2OVrLbzs3mrFzElLWGIf-Ye5AwvleMDiECHnI4

# Redis Configuration
REDIS_URL="redis://localhost:6379"

# JWT Configurationb
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX=5
SESSION_SECRET=your-session-secret-change-this-in-production

# Email Configuration (Nodemailer)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME="WhiskAffair Team"

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/webp,image/avif
UPLOAD_DEST=uploads/

# External Services
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
FACEBOOK_PIXEL_ID=your_facebook_pixel_id

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_KEYS=1000

# Security Headers
HELMET_CSP_DIRECTIVES=default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'

# Analytics Configuration
ANALYTICS_BATCH_SIZE=100
ANALYTICS_FLUSH_INTERVAL=30000

# Monitoring & Health Checks
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true

# Development Only
DEBUG=whisk-affair:*
SWAGGER_ENABLED=true
