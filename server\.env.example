# WhiskAffair Backend Environment Configuration
# Copy this file to .env.local and update with your actual values

# Server Configuration
NODE_ENV=development
PORT=3001
API_VERSION=v1
CORS_ORIGIN=http://localhost:3000

# Supabase Configuration (Required)
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Redis Configuration (Required)
REDIS_URL=redis://localhost:6379

# JWT Configuration (Required - keeping for gradual migration)
JWT_SECRET=your-super-secret-jwt-key-32-chars-minimum-for-security
JWT_REFRESH_SECRET=your-super-secret-refresh-key-32-chars-minimum-for-security
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Security Configuration (Required)
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX=5
SESSION_SECRET=your-session-secret-change-this-in-production

# Email Configuration (Optional - for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=WhiskAffair Team

# File Upload Configuration (Optional)
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/webp,image/avif
UPLOAD_DEST=uploads/

# External Services (Optional)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Analytics Configuration (Optional)
GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
FACEBOOK_PIXEL_ID=your_facebook_pixel_id
ANALYTICS_BATCH_SIZE=100
ANALYTICS_FLUSH_INTERVAL=30000

# Logging Configuration (Optional)
LOG_LEVEL=info
LOG_FILE=logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# Cache Configuration (Optional)
CACHE_TTL=3600
CACHE_MAX_KEYS=1000
