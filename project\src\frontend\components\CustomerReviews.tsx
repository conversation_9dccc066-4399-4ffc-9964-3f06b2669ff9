import React from 'react';
import { Star, Quote } from 'lucide-react';

const CustomerReviews: React.FC = () => {
  const reviews = [
    {
      id: 1,
      name: "<PERSON>",
      rating: 5,
      comment: "Absolutely stunning jewelry! The craftsmanship is exceptional and the pieces are even more beautiful in person. I've received so many compliments.",
      image: "https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop"
    },
    {
      id: 2,
      name: "<PERSON>",
      rating: 5,
      comment: "The perfect gift for my sister's wedding. The packaging was elegant and the quality exceeded my expectations. Will definitely order again!",
      image: "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop"
    },
    {
      id: 3,
      name: "<PERSON>",
      rating: 5,
      comment: "I love how unique each piece is. The attention to detail and the story behind each design makes wearing these pieces feel special.",
      image: "https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop"
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-rose-50 to-pink-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            What Our Customers Say
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover why thousands of customers trust us with their most precious moments
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {reviews.map((review) => (
            <div
              key={review.id}
              className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 relative"
            >
              <div className="absolute -top-4 left-8">
                <div className="bg-rose-500 rounded-full p-3 shadow-lg">
                  <Quote className="h-6 w-6 text-white" />
                </div>
              </div>
              
              <div className="pt-6">
                <div className="flex items-center mb-4">
                  {[...Array(review.rating)].map((_, i) => (
                    <Star
                      key={i}
                      className="h-5 w-5 text-yellow-400 fill-current"
                    />
                  ))}
                </div>
                
                <p className="text-gray-700 mb-6 leading-relaxed">
                  "{review.comment}"
                </p>
                
                <div className="flex items-center">
                  <img
                    src={review.image}
                    alt={review.name}
                    className="w-12 h-12 rounded-full object-cover mr-4 ring-2 ring-rose-100"
                  />
                  <div>
                    <h4 className="font-semibold text-gray-900">{review.name}</h4>
                    <p className="text-sm text-gray-500">Verified Customer</p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <div className="inline-flex items-center space-x-2 bg-white rounded-full px-6 py-3 shadow-md">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className="h-5 w-5 text-yellow-400 fill-current"
                />
              ))}
            </div>
            <span className="text-gray-700 font-medium">4.9/5 from 2,847 reviews</span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CustomerReviews;