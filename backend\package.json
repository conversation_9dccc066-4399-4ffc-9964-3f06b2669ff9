{"name": "whisk-affair-backend", "version": "1.0.0", "description": "WhiskAffair E-commerce Backend API with Analytics and Security", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:types": "supabase gen types typescript --project-id ibocdnngycylvgktbgzw --schema public > src/types/supabase.ts", "db:reset": "supabase db reset", "db:seed": "ts-node src/database/seed.ts"}, "keywords": ["ecommerce", "api", "analytics", "security", "typescript", "express"], "author": "WhiskAffair Team", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.38.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "connect-redis": "^7.1.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "qrcode": "^1.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "redis": "^4.6.10", "sharp": "^0.32.6", "socket.io": "^4.7.4", "speakeasy": "^2.0.0", "stripe": "^14.7.0", "tailwind-merge": "^3.3.1", "uuid": "^9.0.1", "winston": "^3.11.0", "zod": "^3.25.74"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/crypto-js": "^4.2.1", "@types/express": "^4.17.21", "@types/express-session": "^1.17.10", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.9.0", "@types/nodemailer": "^6.4.14", "@types/passport": "^1.0.16", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}