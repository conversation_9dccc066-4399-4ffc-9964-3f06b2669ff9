import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Search,
  Bell,
  Settings,
  User,
  Menu,
  X,
  ChevronDown,
  LogOut,
  Shield,
  HelpCircle,
  Moon,
  Sun,
  Globe,
  Zap,
  Activity,
  TrendingUp,
  DollarSign,
  Users,
  Package,
} from 'lucide-react';
import { cn } from '../../utils/cn';
import Button, { IconButton } from '../ui/Button';

interface AdminHeaderProps {
  sidebarCollapsed: boolean;
  onToggleSidebar: () => void;
}

interface QuickStat {
  label: string;
  value: string;
  change: string;
  trend: 'up' | 'down' | 'neutral';
  icon: React.ReactNode;
}

const AdminHeader: React.FC<AdminHeaderProps> = ({
  sidebarCollapsed,
  onToggleSidebar,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  
  const searchRef = useRef<HTMLDivElement>(null);
  const notificationsRef = useRef<HTMLDivElement>(null);
  const profileRef = useRef<HTMLDivElement>(null);

  // Quick stats for header
  const quickStats: QuickStat[] = [
    {
      label: 'Revenue Today',
      value: '$2,847',
      change: '+12.5%',
      trend: 'up',
      icon: <DollarSign className="w-4 h-4" />,
    },
    {
      label: 'Orders',
      value: '47',
      change: '+8.2%',
      trend: 'up',
      icon: <Package className="w-4 h-4" />,
    },
    {
      label: 'Visitors',
      value: '1,234',
      change: '-2.1%',
      trend: 'down',
      icon: <Users className="w-4 h-4" />,
    },
    {
      label: 'Conversion',
      value: '3.4%',
      change: '+0.8%',
      trend: 'up',
      icon: <TrendingUp className="w-4 h-4" />,
    },
  ];

  // Mock notifications
  const notifications = [
    {
      id: 1,
      title: 'New order received',
      message: 'Order #1234 from John Doe',
      time: '2 minutes ago',
      type: 'order',
      unread: true,
    },
    {
      id: 2,
      title: 'Low inventory alert',
      message: 'Chocolate Cake is running low',
      time: '15 minutes ago',
      type: 'warning',
      unread: true,
    },
    {
      id: 3,
      title: 'Payment received',
      message: '$156.00 payment confirmed',
      time: '1 hour ago',
      type: 'success',
      unread: false,
    },
  ];

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSearch(false);
      }
      if (notificationsRef.current && !notificationsRef.current.contains(event.target as Node)) {
        setShowNotifications(false);
      }
      if (profileRef.current && !profileRef.current.contains(event.target as Node)) {
        setShowProfile(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const unreadCount = notifications.filter(n => n.unread).length;

  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 z-40">
      {/* Main Header */}
      <div className="flex items-center justify-between h-16 px-6">
        {/* Left Section */}
        <div className="flex items-center gap-4">
          <IconButton
            icon={sidebarCollapsed ? <Menu /> : <X />}
            onClick={onToggleSidebar}
            variant="ghost"
            size="md"
            aria-label="Toggle sidebar"
            className="lg:hidden"
          />

          {/* Quick Stats - Hidden on mobile */}
          <div className="hidden xl:flex items-center gap-6">
            {quickStats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center gap-2 px-3 py-2 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors"
              >
                <div className={cn(
                  'p-1.5 rounded-md',
                  stat.trend === 'up' && 'bg-green-100 text-green-600',
                  stat.trend === 'down' && 'bg-red-100 text-red-600',
                  stat.trend === 'neutral' && 'bg-gray-100 text-gray-600'
                )}>
                  {stat.icon}
                </div>
                <div>
                  <p className="text-sm font-semibold text-gray-900">{stat.value}</p>
                  <p className="text-xs text-gray-500">{stat.label}</p>
                </div>
                <span className={cn(
                  'text-xs font-medium',
                  stat.trend === 'up' && 'text-green-600',
                  stat.trend === 'down' && 'text-red-600',
                  stat.trend === 'neutral' && 'text-gray-600'
                )}>
                  {stat.change}
                </span>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Center Section - Search */}
        <div className="flex-1 max-w-2xl mx-8" ref={searchRef}>
          <div className="relative">
            <motion.div
              className={cn(
                'relative flex items-center',
                showSearch && 'ring-2 ring-primary-500 ring-opacity-50'
              )}
              animate={{ scale: showSearch ? 1.02 : 1 }}
              transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            >
              <Search className="absolute left-3 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search orders, products, customers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onFocus={() => setShowSearch(true)}
                className="w-full pl-10 pr-4 py-2.5 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:bg-white focus:border-primary-300 transition-all duration-200"
              />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery('')}
                  className="absolute right-3 p-1 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <X className="w-4 h-4 text-gray-400" />
                </button>
              )}
            </motion.div>

            {/* Search Results Dropdown */}
            <AnimatePresence>
              {showSearch && searchQuery && (
                <motion.div
                  initial={{ opacity: 0, y: -10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -10, scale: 0.95 }}
                  className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50"
                >
                  <div className="p-4">
                    <p className="text-sm text-gray-500 mb-3">Search results for "{searchQuery}"</p>
                    <div className="space-y-2">
                      <div className="p-2 hover:bg-gray-50 rounded-md cursor-pointer">
                        <p className="text-sm font-medium">Order #1234</p>
                        <p className="text-xs text-gray-500">John Doe - $156.00</p>
                      </div>
                      <div className="p-2 hover:bg-gray-50 rounded-md cursor-pointer">
                        <p className="text-sm font-medium">Chocolate Cake</p>
                        <p className="text-xs text-gray-500">Product - 23 in stock</p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center gap-2">
          {/* Theme Toggle */}
          <IconButton
            icon={darkMode ? <Sun /> : <Moon />}
            onClick={() => setDarkMode(!darkMode)}
            variant="ghost"
            size="md"
            aria-label="Toggle theme"
            className="hidden sm:flex"
          />

          {/* Notifications */}
          <div className="relative" ref={notificationsRef}>
            <IconButton
              icon={
                <div className="relative">
                  <Bell className="w-5 h-5" />
                  {unreadCount > 0 && (
                    <motion.span
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"
                    >
                      {unreadCount}
                    </motion.span>
                  )}
                </div>
              }
              onClick={() => setShowNotifications(!showNotifications)}
              variant="ghost"
              size="md"
              aria-label="Notifications"
            />

            {/* Notifications Dropdown */}
            <AnimatePresence>
              {showNotifications && (
                <motion.div
                  initial={{ opacity: 0, y: -10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -10, scale: 0.95 }}
                  className="absolute top-full right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50"
                >
                  <div className="p-4 border-b border-gray-100">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-gray-900">Notifications</h3>
                      <Button variant="ghost" size="sm">
                        Mark all read
                      </Button>
                    </div>
                  </div>
                  <div className="max-h-80 overflow-y-auto">
                    {notifications.map((notification) => (
                      <motion.div
                        key={notification.id}
                        className={cn(
                          'p-4 border-b border-gray-50 hover:bg-gray-50 cursor-pointer transition-colors',
                          notification.unread && 'bg-blue-50'
                        )}
                        whileHover={{ x: 4 }}
                      >
                        <div className="flex items-start gap-3">
                          <div className={cn(
                            'w-2 h-2 rounded-full mt-2',
                            notification.unread ? 'bg-blue-500' : 'bg-gray-300'
                          )} />
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900">
                              {notification.title}
                            </p>
                            <p className="text-sm text-gray-600 mt-1">
                              {notification.message}
                            </p>
                            <p className="text-xs text-gray-500 mt-2">
                              {notification.time}
                            </p>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                  <div className="p-4 border-t border-gray-100">
                    <Button variant="ghost" size="sm" fullWidth>
                      View all notifications
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Profile Dropdown */}
          <div className="relative" ref={profileRef}>
            <button
              onClick={() => setShowProfile(!showProfile)}
              className="flex items-center gap-2 p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-white" />
              </div>
              <div className="hidden sm:block text-left">
                <p className="text-sm font-medium text-gray-900">Admin User</p>
                <p className="text-xs text-gray-500"><EMAIL></p>
              </div>
              <ChevronDown className="w-4 h-4 text-gray-400" />
            </button>

            {/* Profile Dropdown */}
            <AnimatePresence>
              {showProfile && (
                <motion.div
                  initial={{ opacity: 0, y: -10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -10, scale: 0.95 }}
                  className="absolute top-full right-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50"
                >
                  <div className="p-4 border-b border-gray-100">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
                        <User className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <p className="font-semibold text-gray-900">Admin User</p>
                        <p className="text-sm text-gray-500"><EMAIL></p>
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mt-1">
                          Super Admin
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-2">
                    <button className="w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors">
                      <User className="w-4 h-4" />
                      Profile Settings
                    </button>
                    <button className="w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors">
                      <Shield className="w-4 h-4" />
                      Security
                    </button>
                    <button className="w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors">
                      <HelpCircle className="w-4 h-4" />
                      Help & Support
                    </button>
                  </div>
                  
                  <div className="p-2 border-t border-gray-100">
                    <button className="w-full flex items-center gap-3 px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md transition-colors">
                      <LogOut className="w-4 h-4" />
                      Sign Out
                    </button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>

      {/* Secondary Header - Quick Actions */}
      <div className="hidden lg:block border-t border-gray-100 bg-gray-50">
        <div className="flex items-center justify-between px-6 py-2">
          <div className="flex items-center gap-4">
            <span className="text-sm text-gray-600">Quick Actions:</span>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm">
                <Package className="w-4 h-4 mr-2" />
                Add Product
              </Button>
              <Button variant="ghost" size="sm">
                <Users className="w-4 h-4 mr-2" />
                View Customers
              </Button>
              <Button variant="ghost" size="sm">
                <Activity className="w-4 h-4 mr-2" />
                Analytics
              </Button>
            </div>
          </div>
          
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Activity className="w-4 h-4 text-green-500" />
            <span>All systems operational</span>
          </div>
        </div>
      </div>
    </header>
  );
};

export default AdminHeader;
