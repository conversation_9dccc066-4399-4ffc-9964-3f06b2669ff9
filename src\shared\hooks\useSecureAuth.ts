import { useState, useEffect, useCallback } from 'react';
import { useAuthStore } from '../store/authStore';
import { SecureStorage, isTokenExpired, validateData, userValidationSchemas } from '../utils/security';

interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
  captchaToken?: string;
}

interface TwoFactorData {
  code: string;
  trustDevice?: boolean;
}

interface SecurityMetrics {
  failedAttempts: number;
  lastFailedAttempt: string | null;
  isLocked: boolean;
  lockoutExpiry: string | null;
}

export const useSecureAuth = () => {
  const { user, token, isAuthenticated, login, logout, verifyTwoFactor } = useAuthStore();
  const [isLoading, setIsLoading] = useState(false);
  const [securityMetrics, setSecurityMetrics] = useState<SecurityMetrics>({
    failedAttempts: 0,
    lastFailedAttempt: null,
    isLocked: false,
    lockoutExpiry: null,
  });

  // Security configuration
  const MAX_FAILED_ATTEMPTS = 5;
  const LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes
  const TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000; // 5 minutes before expiry

  // Load security metrics from storage
  useEffect(() => {
    const storedMetrics = SecureStorage.getItem('security-metrics');
    if (storedMetrics) {
      try {
        const metrics = JSON.parse(storedMetrics);
        setSecurityMetrics(metrics);
        
        // Check if lockout has expired
        if (metrics.isLocked && metrics.lockoutExpiry) {
          const now = new Date().getTime();
          const expiry = new Date(metrics.lockoutExpiry).getTime();
          
          if (now > expiry) {
            resetSecurityMetrics();
          }
        }
      } catch (error) {
        console.error('Failed to load security metrics:', error);
        resetSecurityMetrics();
      }
    }
  }, []);

  // Auto token refresh
  useEffect(() => {
    if (!token || !isAuthenticated) return;

    const checkTokenExpiry = () => {
      if (isTokenExpired(token)) {
        console.log('Token expired, logging out');
        handleLogout();
        return;
      }

      // Check if token needs refresh (5 minutes before expiry)
      const payload = JSON.parse(atob(token.split('.')[1]));
      const expiryTime = payload.exp * 1000;
      const currentTime = Date.now();
      
      if (expiryTime - currentTime < TOKEN_REFRESH_THRESHOLD) {
        refreshToken();
      }
    };

    const interval = setInterval(checkTokenExpiry, 60000); // Check every minute
    return () => clearInterval(interval);
  }, [token, isAuthenticated]);

  // Reset security metrics
  const resetSecurityMetrics = useCallback(() => {
    const resetMetrics: SecurityMetrics = {
      failedAttempts: 0,
      lastFailedAttempt: null,
      isLocked: false,
      lockoutExpiry: null,
    };
    
    setSecurityMetrics(resetMetrics);
    SecureStorage.removeItem('security-metrics');
  }, []);

  // Update security metrics
  const updateSecurityMetrics = useCallback((failed: boolean) => {
    const now = new Date().toISOString();
    
    if (failed) {
      const newFailedAttempts = securityMetrics.failedAttempts + 1;
      const shouldLock = newFailedAttempts >= MAX_FAILED_ATTEMPTS;
      
      const newMetrics: SecurityMetrics = {
        failedAttempts: newFailedAttempts,
        lastFailedAttempt: now,
        isLocked: shouldLock,
        lockoutExpiry: shouldLock ? new Date(Date.now() + LOCKOUT_DURATION).toISOString() : null,
      };
      
      setSecurityMetrics(newMetrics);
      SecureStorage.setItem('security-metrics', JSON.stringify(newMetrics));
      
      if (shouldLock) {
        console.warn('Account locked due to too many failed attempts');
      }
    } else {
      // Successful login - reset metrics
      resetSecurityMetrics();
    }
  }, [securityMetrics, resetSecurityMetrics]);

  // Secure login with validation and rate limiting
  const handleSecureLogin = useCallback(async (credentials: LoginCredentials) => {
    // Check if account is locked
    if (securityMetrics.isLocked) {
      const lockoutExpiry = securityMetrics.lockoutExpiry ? new Date(securityMetrics.lockoutExpiry) : null;
      const now = new Date();
      
      if (lockoutExpiry && now < lockoutExpiry) {
        const remainingTime = Math.ceil((lockoutExpiry.getTime() - now.getTime()) / 60000);
        throw new Error(`Account locked. Try again in ${remainingTime} minutes.`);
      } else {
        resetSecurityMetrics();
      }
    }

    setIsLoading(true);

    try {
      // Validate input
      const emailValidation = validateData(credentials.email, userValidationSchemas.email);
      const passwordValidation = validateData(credentials.password, userValidationSchemas.password);

      if (!emailValidation.success) {
        throw new Error(emailValidation.errors?.[0] || 'Invalid email');
      }

      if (!passwordValidation.success) {
        throw new Error(passwordValidation.errors?.[0] || 'Invalid password');
      }

      // Attempt login
      await login(credentials.email, credentials.password);
      
      // Success - reset security metrics
      updateSecurityMetrics(false);
      
      // Store remember me preference
      if (credentials.rememberMe) {
        SecureStorage.setItem('remember-me', 'true');
      }

      return { success: true };
    } catch (error) {
      // Failed login - update security metrics
      updateSecurityMetrics(true);
      
      setIsLoading(false);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [login, securityMetrics, updateSecurityMetrics, resetSecurityMetrics]);

  // Secure 2FA verification
  const handleTwoFactorVerification = useCallback(async (data: TwoFactorData) => {
    setIsLoading(true);

    try {
      // Validate 2FA code format
      if (!/^\d{6}$/.test(data.code)) {
        throw new Error('Invalid 2FA code format');
      }

      await verifyTwoFactor(data.code);

      // Store trust device preference
      if (data.trustDevice) {
        SecureStorage.setItem('trusted-device', 'true');
      }

      return { success: true };
    } catch (error) {
      updateSecurityMetrics(true);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [verifyTwoFactor, updateSecurityMetrics]);

  // Secure logout with cleanup
  const handleLogout = useCallback(() => {
    // Clear all stored data
    SecureStorage.removeItem('remember-me');
    SecureStorage.removeItem('trusted-device');
    SecureStorage.removeItem('auth-storage');
    
    // Clear session storage
    sessionStorage.clear();
    
    // Reset security metrics on successful logout
    resetSecurityMetrics();
    
    // Call store logout
    logout();
  }, [logout, resetSecurityMetrics]);

  // Refresh token
  const refreshToken = useCallback(async () => {
    try {
      // In a real implementation, this would call your refresh token endpoint
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Token refresh failed');
      }

      const data = await response.json();
      
      // Update token in store
      useAuthStore.getState().setToken(data.token);
      
      console.log('Token refreshed successfully');
    } catch (error) {
      console.error('Token refresh failed:', error);
      handleLogout();
    }
  }, [token, handleLogout]);

  // Check password strength
  const checkPasswordStrength = useCallback((password: string) => {
    const checks = {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      numbers: /\d/.test(password),
      symbols: /[!@#$%^&*(),.?":{}|<>]/.test(password),
    };

    const score = Object.values(checks).filter(Boolean).length;
    const strength = score < 3 ? 'weak' : score < 5 ? 'medium' : 'strong';

    return {
      score,
      strength,
      checks,
      isValid: score >= 4, // Require at least 4 criteria
    };
  }, []);

  // Generate secure session ID
  const generateSessionId = useCallback(() => {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }, []);

  // Validate session integrity
  const validateSession = useCallback(() => {
    if (!isAuthenticated || !token) return false;

    try {
      // Check token expiry
      if (isTokenExpired(token)) {
        console.log('Session expired');
        handleLogout();
        return false;
      }

      // Check for suspicious activity (e.g., multiple tabs, unusual patterns)
      const lastActivity = SecureStorage.getItem('last-activity');
      const currentTime = Date.now();
      
      if (lastActivity) {
        const timeDiff = currentTime - parseInt(lastActivity);
        // If no activity for more than 30 minutes, require re-authentication
        if (timeDiff > 30 * 60 * 1000) {
          console.log('Session inactive for too long');
          handleLogout();
          return false;
        }
      }

      // Update last activity
      SecureStorage.setItem('last-activity', currentTime.toString());
      
      return true;
    } catch (error) {
      console.error('Session validation failed:', error);
      handleLogout();
      return false;
    }
  }, [isAuthenticated, token, handleLogout]);

  // Get security status
  const getSecurityStatus = useCallback(() => {
    return {
      isAuthenticated,
      user,
      isLoading,
      securityMetrics,
      isLocked: securityMetrics.isLocked,
      remainingAttempts: Math.max(0, MAX_FAILED_ATTEMPTS - securityMetrics.failedAttempts),
      sessionValid: validateSession(),
    };
  }, [isAuthenticated, user, isLoading, securityMetrics, validateSession]);

  return {
    // Auth state
    isAuthenticated,
    user,
    isLoading,
    
    // Security metrics
    securityMetrics,
    isLocked: securityMetrics.isLocked,
    remainingAttempts: Math.max(0, MAX_FAILED_ATTEMPTS - securityMetrics.failedAttempts),
    
    // Auth methods
    login: handleSecureLogin,
    logout: handleLogout,
    verifyTwoFactor: handleTwoFactorVerification,
    
    // Security utilities
    checkPasswordStrength,
    generateSessionId,
    validateSession,
    getSecurityStatus,
    resetSecurityMetrics,
  };
};
