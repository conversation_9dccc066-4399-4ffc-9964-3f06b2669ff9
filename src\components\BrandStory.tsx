import React from 'react';
import { motion } from 'framer-motion';
import { Quote, Heart, Award, Users } from 'lucide-react';

const BrandStory: React.FC = () => {
  const stats = [
    { icon: Heart, number: '10K+', label: 'Happy Customers' },
    { icon: Award, number: '50+', label: 'Premium Recipes' },
    { icon: Users, number: '5+', label: 'Years of Excellence' },
  ];

  return (
    <section id="story" className="py-20 bg-gradient-blush">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left: Founder's Story */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="relative">
              <div className="absolute -top-6 -left-6 text-gold/20">
                <Quote className="w-16 h-16" />
              </div>
              
              <h2 className="font-playfair text-4xl md:text-5xl font-bold text-taupe mb-6">
                A Sweet Journey
                <span className="block text-gold">Born from Love</span>
              </h2>
              
              <div className="space-y-6 font-montserrat text-taupe/80 leading-relaxed">
                <p className="text-lg">
                  What started as a mother's quest to create healthier treats for her family 
                  has blossomed into Mumbai's most beloved butter-free dessert destination.
                </p>
                
                <p>
                  Jinali's passion for baking began in her grandmother's kitchen, where she learned 
                  that the secret ingredient to any perfect dessert isn't just technique—it's love. 
                  When her daughter developed dairy sensitivities, Jinali embarked on a mission to 
                  recreate classic treats without compromising on taste or luxury.
                </p>
                
                <p>
                  Today, each Whisk Affair creation is a testament to the belief that dietary 
                  restrictions shouldn't mean sacrificing indulgence. Every cookie, every cake, 
                  every bite is crafted with premium ingredients and an unwavering commitment to excellence.
                </p>
              </div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                viewport={{ once: true }}
                className="mt-8 p-6 bg-white/50 rounded-2xl border border-gold/20"
              >
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-gold/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Heart className="w-6 h-6 text-gold" />
                  </div>
                  <div>
                    <h3 className="font-playfair text-xl font-semibold text-taupe mb-2">
                      Our Promise
                    </h3>
                    <p className="font-montserrat text-taupe/70 italic">
                      "Every creation that leaves our kitchen carries with it the same love and care 
                      I put into treats for my own family. That's the Whisk Affair promise."
                    </p>
                    <p className="font-montserrat font-medium text-gold mt-2">— Jinali</p>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* Right: Visual Elements */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative"
          >
            {/* Founder's Portrait Placeholder */}
            <div className="relative">
              <div className="aspect-[4/5] bg-cream rounded-3xl shadow-soft overflow-hidden">
                <div className="w-full h-full bg-gradient-to-br from-blush to-gold-light flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-32 h-32 bg-taupe/10 rounded-full mx-auto mb-4 flex items-center justify-center">
                      <Users className="w-16 h-16 text-taupe/30" />
                    </div>
                    <p className="font-montserrat text-taupe/60">Founder's Portrait</p>
                  </div>
                </div>
              </div>
              
              {/* Decorative elements */}
              <div className="absolute -bottom-4 -right-4 w-24 h-24 bg-gold/20 rounded-full"></div>
              <div className="absolute -top-8 -left-8 w-16 h-16 bg-blush/30 rounded-full"></div>
            </div>

            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              viewport={{ once: true }}
              className="mt-12 grid grid-cols-3 gap-6"
            >
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="w-12 h-12 bg-gold/20 rounded-2xl flex items-center justify-center mx-auto mb-3">
                    <stat.icon className="w-6 h-6 text-gold" />
                  </div>
                  <div className="font-playfair text-2xl font-bold text-taupe">
                    {stat.number}
                  </div>
                  <div className="font-montserrat text-sm text-taupe/60">
                    {stat.label}
                  </div>
                </div>
              ))}
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default BrandStory;