import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { NavLink, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  FileText,
  Settings,
  X,
  ChevronRight,
} from 'lucide-react';
import { useAuthStore } from '../../store/authStore';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  const location = useLocation();
  const { user } = useAuthStore();

  const navigationItems = [
    {
      name: 'Dashboard',
      href: '/',
      icon: LayoutDashboard,
      permissions: [],
    },
    {
      name: 'Products',
      href: '/products',
      icon: Package,
      permissions: ['products.read'],
      children: [
        { name: 'All Products', href: '/products' },
        { name: 'Categories', href: '/products/categories' },
        { name: 'Inventory', href: '/products/inventory' },
      ],
    },
    {
      name: 'Orders',
      href: '/orders',
      icon: ShoppingCart,
      permissions: ['orders.read'],
      children: [
        { name: 'All Orders', href: '/orders' },
        { name: 'Pending', href: '/orders/pending' },
        { name: 'Returns', href: '/orders/returns' },
      ],
    },
    {
      name: 'Users',
      href: '/users',
      icon: Users,
      permissions: ['users.read'],
      children: [
        { name: 'Customers', href: '/users/customers' },
        { name: 'Admins', href: '/users/admins' },
      ],
    },
    {
      name: 'Content',
      href: '/content',
      icon: FileText,
      permissions: ['content.read'],
      children: [
        { name: 'Pages', href: '/content/pages' },
        { name: 'Blog', href: '/content/blog' },
        { name: 'Media', href: '/content/media' },
      ],
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: Settings,
      permissions: ['system.read'],
      children: [
        { name: 'General', href: '/settings' },
        { name: 'Payment', href: '/settings/payment' },
        { name: 'Shipping', href: '/settings/shipping' },
      ],
    },
  ];

  const hasPermission = (permissions: string[]) => {
    if (permissions.length === 0) return true;
    return permissions.every(permission => user?.permissions.includes(permission));
  };

  const isActiveRoute = (href: string) => {
    if (href === '/') return location.pathname === '/';
    return location.pathname.startsWith(href);
  };

  return (
    <>
      {/* Desktop Sidebar */}
      <div className="hidden lg:flex lg:flex-shrink-0">
        <div className="flex flex-col w-64">
          <div className="flex flex-col flex-grow bg-white border-r border-gray-200 pt-5 pb-4 overflow-y-auto">
            <div className="flex items-center flex-shrink-0 px-4">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                  <LayoutDashboard className="w-5 h-5 text-white" />
                </div>
                <div className="ml-3">
                  <h1 className="text-lg font-semibold text-gray-900">Admin Panel</h1>
                  <p className="text-xs text-gray-500">E-commerce CMS</p>
                </div>
              </div>
            </div>
            
            <nav className="mt-8 flex-1 px-2 space-y-1">
              {navigationItems.map((item) => {
                if (!hasPermission(item.permissions)) return null;
                
                return (
                  <div key={item.name}>
                    <NavLink
                      to={item.href}
                      className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                        isActiveRoute(item.href)
                          ? 'bg-primary-100 text-primary-900'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                    >
                      <item.icon
                        className={`mr-3 flex-shrink-0 h-5 w-5 ${
                          isActiveRoute(item.href) ? 'text-primary-500' : 'text-gray-400'
                        }`}
                      />
                      {item.name}
                      {item.children && (
                        <ChevronRight
                          className={`ml-auto h-4 w-4 transition-transform ${
                            isActiveRoute(item.href) ? 'rotate-90' : ''
                          }`}
                        />
                      )}
                    </NavLink>
                    
                    {item.children && isActiveRoute(item.href) && (
                      <div className="ml-8 mt-1 space-y-1">
                        {item.children.map((child) => (
                          <NavLink
                            key={child.name}
                            to={child.href}
                            className={`group flex items-center px-2 py-1 text-sm rounded-md transition-colors ${
                              location.pathname === child.href
                                ? 'text-primary-600 font-medium'
                                : 'text-gray-500 hover:text-gray-700'
                            }`}
                          >
                            {child.name}
                          </NavLink>
                        ))}
                      </div>
                    )}
                  </div>
                );
              })}
            </nav>
          </div>
        </div>
      </div>

      {/* Mobile Sidebar */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ x: -300 }}
            animate={{ x: 0 }}
            exit={{ x: -300 }}
            transition={{ type: 'spring', damping: 30, stiffness: 300 }}
            className="lg:hidden fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl"
          >
            <div className="flex flex-col h-full">
              <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                    <LayoutDashboard className="w-5 h-5 text-white" />
                  </div>
                  <div className="ml-3">
                    <h1 className="text-lg font-semibold text-gray-900">Admin Panel</h1>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              
              <nav className="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
                {navigationItems.map((item) => {
                  if (!hasPermission(item.permissions)) return null;
                  
                  return (
                    <NavLink
                      key={item.name}
                      to={item.href}
                      onClick={onClose}
                      className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                        isActiveRoute(item.href)
                          ? 'bg-primary-100 text-primary-900'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                    >
                      <item.icon
                        className={`mr-3 flex-shrink-0 h-5 w-5 ${
                          isActiveRoute(item.href) ? 'text-primary-500' : 'text-gray-400'
                        }`}
                      />
                      {item.name}
                    </NavLink>
                  );
                })}
              </nav>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default Sidebar;