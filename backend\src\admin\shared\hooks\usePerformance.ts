import { useState, useEffect, useCallback, useRef } from 'react';

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  interactionTime: number;
  memoryUsage: number;
  connectionType: string;
  isSlowConnection: boolean;
}

interface LazyLoadOptions {
  rootMargin?: string;
  threshold?: number;
  triggerOnce?: boolean;
}

interface ImageOptimizationOptions {
  quality?: number;
  format?: 'webp' | 'avif' | 'auto';
  sizes?: string;
  loading?: 'lazy' | 'eager';
}

export const usePerformance = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadTime: 0,
    renderTime: 0,
    interactionTime: 0,
    memoryUsage: 0,
    connectionType: 'unknown',
    isSlowConnection: false,
  });

  const [isVisible, setIsVisible] = useState(false);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const elementRef = useRef<HTMLElement | null>(null);

  // Performance monitoring
  useEffect(() => {
    const measurePerformance = () => {
      if (typeof window === 'undefined') return;

      // Get navigation timing
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const loadTime = navigation.loadEventEnd - navigation.loadEventStart;
      const renderTime = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;

      // Get memory usage (if available)
      const memory = (performance as any).memory;
      const memoryUsage = memory ? memory.usedJSHeapSize / 1024 / 1024 : 0; // MB

      // Get connection info
      const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
      const connectionType = connection ? connection.effectiveType || connection.type : 'unknown';
      const isSlowConnection = connection ? connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g' : false;

      setMetrics({
        loadTime,
        renderTime,
        interactionTime: 0, // Will be updated by interaction measurements
        memoryUsage,
        connectionType,
        isSlowConnection,
      });
    };

    // Measure on load
    if (document.readyState === 'complete') {
      measurePerformance();
    } else {
      window.addEventListener('load', measurePerformance);
    }

    // Monitor memory usage periodically
    const memoryInterval = setInterval(() => {
      const memory = (performance as any).memory;
      if (memory) {
        setMetrics(prev => ({
          ...prev,
          memoryUsage: memory.usedJSHeapSize / 1024 / 1024,
        }));
      }
    }, 10000); // Every 10 seconds

    return () => {
      window.removeEventListener('load', measurePerformance);
      clearInterval(memoryInterval);
    };
  }, []);

  // Lazy loading hook
  const useLazyLoad = useCallback((options: LazyLoadOptions = {}) => {
    const { rootMargin = '50px', threshold = 0.1, triggerOnce = true } = options;

    useEffect(() => {
      if (!elementRef.current) return undefined;

      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              setIsVisible(true);
              if (triggerOnce) {
                observer.unobserve(entry.target);
              }
            } else if (!triggerOnce) {
              setIsVisible(false);
            }
          });
        },
        { rootMargin, threshold }
      );

      observer.observe(elementRef.current);
      observerRef.current = observer;

      return () => {
        observer.disconnect();
      };
    }, [rootMargin, threshold, triggerOnce]);

    return { ref: elementRef, isVisible };
  }, []);

  // Image optimization
  const optimizeImageUrl = useCallback((src: string, options: ImageOptimizationOptions = {}) => {
    const { quality = 80, format = 'auto', sizes, loading = 'lazy' } = options;

    // Check if browser supports WebP
    const supportsWebP = () => {
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    };

    // Check if browser supports AVIF
    const supportsAVIF = () => {
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      return canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0;
    };

    // Determine best format
    let targetFormat = format;
    if (format === 'auto') {
      if (supportsAVIF()) {
        targetFormat = 'avif';
      } else if (supportsWebP()) {
        targetFormat = 'webp';
      }
    }

    // Build optimized URL (assuming you have an image optimization service)
    const params = new URLSearchParams();
    params.append('q', quality.toString());
    if (targetFormat !== 'auto') {
      params.append('f', targetFormat);
    }
    if (sizes) {
      params.append('sizes', sizes);
    }

    const optimizedUrl = src.includes('?') 
      ? `${src}&${params.toString()}`
      : `${src}?${params.toString()}`;

    return {
      src: optimizedUrl,
      loading,
      decoding: 'async' as const,
    };
  }, []);

  // Preload critical resources
  const preloadResource = useCallback((href: string, as: string, type?: string) => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = as;
    if (type) link.type = type;
    
    document.head.appendChild(link);

    return () => {
      document.head.removeChild(link);
    };
  }, []);

  // Prefetch resources
  const prefetchResource = useCallback((href: string) => {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = href;
    
    document.head.appendChild(link);

    return () => {
      document.head.removeChild(link);
    };
  }, []);

  // Measure interaction timing
  const measureInteraction = useCallback((interactionName: string) => {
    const startTime = performance.now();

    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;

      // Update metrics
      setMetrics(prev => ({
        ...prev,
        interactionTime: duration,
      }));

      // Log to analytics
      if (typeof (window as any).gtag !== 'undefined') {
        (window as any).gtag('event', 'timing_complete', {
          name: interactionName,
          value: Math.round(duration),
        });
      }

      console.log(`${interactionName} took ${duration.toFixed(2)}ms`);
    };
  }, []);

  // Debounce function for performance
  const useDebounce = useCallback(<T>(value: T, delay: number): T => {
    const [debouncedValue, setDebouncedValue] = useState<T>(value);

    useEffect(() => {
      const handler = setTimeout(() => {
        setDebouncedValue(value);
      }, delay);

      return () => {
        clearTimeout(handler);
      };
    }, [value, delay]);

    return debouncedValue;
  }, []);

  // Throttle function for performance
  const useThrottle = useCallback(<T extends (...args: any[]) => any>(
    callback: T,
    delay: number
  ): T => {
    const lastRun = useRef(Date.now());

    return useCallback(
      ((...args) => {
        if (Date.now() - lastRun.current >= delay) {
          callback(...args);
          lastRun.current = Date.now();
        }
      }) as T,
      [callback, delay]
    );
  }, []);

  // Virtual scrolling for large lists
  const useVirtualScroll = useCallback((
    items: any[],
    itemHeight: number,
    containerHeight: number
  ) => {
    const [scrollTop, setScrollTop] = useState(0);
    
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    );
    
    const visibleItems = items.slice(startIndex, endIndex);
    const totalHeight = items.length * itemHeight;
    const offsetY = startIndex * itemHeight;

    return {
      visibleItems,
      totalHeight,
      offsetY,
      onScroll: (e: React.UIEvent<HTMLDivElement>) => {
        setScrollTop(e.currentTarget.scrollTop);
      },
    };
  }, []);

  // Bundle splitting helper
  const loadComponent = useCallback(async (importFn: () => Promise<any>) => {
    const startTime = performance.now();
    
    try {
      const component = await importFn();
      const loadTime = performance.now() - startTime;
      
      console.log(`Component loaded in ${loadTime.toFixed(2)}ms`);
      
      return component;
    } catch (error) {
      console.error('Failed to load component:', error);
      throw error;
    }
  }, []);

  // Resource hints
  const addResourceHints = useCallback((hints: Array<{ href: string; rel: string; as?: string }>) => {
    hints.forEach(({ href, rel, as }) => {
      const link = document.createElement('link');
      link.rel = rel;
      link.href = href;
      if (as) link.as = as;
      document.head.appendChild(link);
    });
  }, []);

  // Performance budget checker
  const checkPerformanceBudget = useCallback(() => {
    const budgets = {
      loadTime: 3000, // 3 seconds
      renderTime: 1000, // 1 second
      memoryUsage: 50, // 50 MB
    };

    const violations = [];

    if (metrics.loadTime > budgets.loadTime) {
      violations.push(`Load time exceeded budget: ${metrics.loadTime}ms > ${budgets.loadTime}ms`);
    }

    if (metrics.renderTime > budgets.renderTime) {
      violations.push(`Render time exceeded budget: ${metrics.renderTime}ms > ${budgets.renderTime}ms`);
    }

    if (metrics.memoryUsage > budgets.memoryUsage) {
      violations.push(`Memory usage exceeded budget: ${metrics.memoryUsage}MB > ${budgets.memoryUsage}MB`);
    }

    if (violations.length > 0) {
      console.warn('Performance budget violations:', violations);
    }

    return {
      passed: violations.length === 0,
      violations,
      budgets,
      metrics,
    };
  }, [metrics]);

  return {
    // Performance metrics
    metrics,
    
    // Lazy loading
    useLazyLoad,
    
    // Image optimization
    optimizeImageUrl,
    
    // Resource management
    preloadResource,
    prefetchResource,
    addResourceHints,
    
    // Performance utilities
    measureInteraction,
    useDebounce,
    useThrottle,
    useVirtualScroll,
    loadComponent,
    
    // Performance monitoring
    checkPerformanceBudget,
  };
};
