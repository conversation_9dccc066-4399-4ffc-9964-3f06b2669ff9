{"name": "whiskaffair-monorepo", "version": "1.0.0", "description": "WhiskAffair E-commerce Platform - Full Stack Application", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "concurrently \"npm run start:frontend\" \"npm run start:backend\"", "start:frontend": "cd frontend && npm run preview", "start:backend": "cd backend && npm run start", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && npm run test", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "clean": "rm -rf node_modules frontend/node_modules backend/node_modules frontend/dist backend/dist", "db:types": "cd backend && npm run db:types", "db:reset": "cd backend && npm run db:reset", "db:seed": "cd backend && npm run db:seed"}, "keywords": ["ecommerce", "react", "express", "typescript", "supabase", "analytics", "cms", "admin-panel"], "author": "WhiskAffair Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}