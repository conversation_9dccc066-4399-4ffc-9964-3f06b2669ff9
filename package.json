{"name": "ecommerce-admin-cms", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server": "node server/index.js"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.20.1", "framer-motion": "^10.16.4", "lucide-react": "^0.344.0", "recharts": "^2.8.0", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "axios": "^1.6.2", "date-fns": "^2.30.0", "react-table": "^7.8.0", "react-dropzone": "^14.2.3", "react-hot-toast": "^2.4.1", "zustand": "^4.4.7", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "nodemailer": "^6.9.7", "speakeasy": "^2.0.0", "qrcode": "^1.5.3", "winston": "^3.11.0", "express-validator": "^7.0.1", "compression": "^1.7.4", "express-mongo-sanitize": "^2.2.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}