import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { NavLink, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  BarChart3,
  Settings,
  ChevronDown,
  ChevronRight,
  Store,
  CreditCard,
  FileText,
  Shield,
  Zap,
  Globe,
  MessageSquare,
  Gift,
  Truck,
  Tag,
  UserCheck,
  Bell,
  HelpCircle,
  Clock,
  Plus,
} from 'lucide-react';
import { cn } from '../../utils/cn';

interface SidebarProps {
  collapsed: boolean;
  onToggle: () => void;
}

interface NavItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  href?: string;
  badge?: string | number;
  children?: NavItem[];
  comingSoon?: boolean;
}

const navigationItems: NavItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: <LayoutDashboard className="w-5 h-5" />,
    href: '/admin',
  },
  {
    id: 'orders',
    label: 'Orders',
    icon: <ShoppingCart className="w-5 h-5" />,
    badge: '12',
    children: [
      { id: 'all-orders', label: 'All Orders', icon: <FileText className="w-4 h-4" />, href: '/admin/orders' },
      { id: 'pending', label: 'Pending', icon: <Clock className="w-4 h-4" />, href: '/admin/orders/pending', badge: '5' },
      { id: 'processing', label: 'Processing', icon: <Zap className="w-4 h-4" />, href: '/admin/orders/processing', badge: '3' },
      { id: 'shipped', label: 'Shipped', icon: <Truck className="w-4 h-4" />, href: '/admin/orders/shipped' },
      { id: 'delivered', label: 'Delivered', icon: <UserCheck className="w-4 h-4" />, href: '/admin/orders/delivered' },
    ],
  },
  {
    id: 'products',
    label: 'Products',
    icon: <Package className="w-5 h-5" />,
    children: [
      { id: 'all-products', label: 'All Products', icon: <Package className="w-4 h-4" />, href: '/admin/products' },
      { id: 'add-product', label: 'Add Product', icon: <Plus className="w-4 h-4" />, href: '/admin/products/new' },
      { id: 'categories', label: 'Categories', icon: <Tag className="w-4 h-4" />, href: '/admin/categories' },
      { id: 'inventory', label: 'Inventory', icon: <BarChart3 className="w-4 h-4" />, href: '/admin/inventory' },
      { id: 'gift-cards', label: 'Gift Cards', icon: <Gift className="w-4 h-4" />, href: '/admin/gift-cards' },
    ],
  },
  {
    id: 'customers',
    label: 'Customers',
    icon: <Users className="w-5 h-5" />,
    children: [
      { id: 'all-customers', label: 'All Customers', icon: <Users className="w-4 h-4" />, href: '/admin/customers' },
      { id: 'segments', label: 'Segments', icon: <UserCheck className="w-4 h-4" />, href: '/admin/customers/segments' },
      { id: 'reviews', label: 'Reviews', icon: <MessageSquare className="w-4 h-4" />, href: '/admin/reviews' },
    ],
  },
  {
    id: 'analytics',
    label: 'Analytics',
    icon: <BarChart3 className="w-5 h-5" />,
    children: [
      { id: 'overview', label: 'Overview', icon: <BarChart3 className="w-4 h-4" />, href: '/admin/analytics' },
      { id: 'sales', label: 'Sales Reports', icon: <CreditCard className="w-4 h-4" />, href: '/admin/analytics/sales' },
      { id: 'products-analytics', label: 'Product Analytics', icon: <Package className="w-4 h-4" />, href: '/admin/analytics/products' },
      { id: 'customer-analytics', label: 'Customer Insights', icon: <Users className="w-4 h-4" />, href: '/admin/analytics/customers' },
      { id: 'marketing', label: 'Marketing', icon: <Globe className="w-4 h-4" />, href: '/admin/analytics/marketing' },
    ],
  },
  {
    id: 'marketing',
    label: 'Marketing',
    icon: <Globe className="w-5 h-5" />,
    children: [
      { id: 'campaigns', label: 'Campaigns', icon: <Globe className="w-4 h-4" />, href: '/admin/marketing/campaigns' },
      { id: 'discounts', label: 'Discounts', icon: <Tag className="w-4 h-4" />, href: '/admin/marketing/discounts' },
      { id: 'email', label: 'Email Marketing', icon: <MessageSquare className="w-4 h-4" />, href: '/admin/marketing/email' },
    ],
  },
  {
    id: 'finances',
    label: 'Finances',
    icon: <CreditCard className="w-5 h-5" />,
    children: [
      { id: 'payments', label: 'Payments', icon: <CreditCard className="w-4 h-4" />, href: '/admin/finances/payments' },
      { id: 'payouts', label: 'Payouts', icon: <Truck className="w-4 h-4" />, href: '/admin/finances/payouts' },
      { id: 'taxes', label: 'Taxes', icon: <FileText className="w-4 h-4" />, href: '/admin/finances/taxes' },
    ],
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: <Settings className="w-5 h-5" />,
    children: [
      { id: 'general', label: 'General', icon: <Settings className="w-4 h-4" />, href: '/admin/settings' },
      { id: 'store', label: 'Store Details', icon: <Store className="w-4 h-4" />, href: '/admin/settings/store' },
      { id: 'shipping', label: 'Shipping', icon: <Truck className="w-4 h-4" />, href: '/admin/settings/shipping' },
      { id: 'payments-settings', label: 'Payments', icon: <CreditCard className="w-4 h-4" />, href: '/admin/settings/payments' },
      { id: 'notifications', label: 'Notifications', icon: <Bell className="w-4 h-4" />, href: '/admin/settings/notifications' },
      { id: 'security', label: 'Security', icon: <Shield className="w-4 h-4" />, href: '/admin/settings/security' },
    ],
  },
];

const AdminSidebar: React.FC<SidebarProps> = ({ collapsed, onToggle }) => {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set(['dashboard']));
  const location = useLocation();

  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const isActive = (href?: string) => {
    if (!href) return false;
    return location.pathname === href || location.pathname.startsWith(href + '/');
  };

  const isParentActive = (item: NavItem): boolean => {
    if (item.href && isActive(item.href)) return true;
    return item.children?.some(child => isActive(child.href)) || false;
  };

  const renderNavItem = (item: NavItem, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.has(item.id);
    const itemIsActive = isActive(item.href);
    const parentActive = isParentActive(item);

    const itemContent = (
      <motion.div
        className={cn(
          'flex items-center justify-between w-full px-3 py-2.5 rounded-lg transition-all duration-200',
          'hover:bg-gray-100 group relative',
          level > 0 && 'ml-6 py-2',
          itemIsActive && 'bg-primary-50 text-primary-700 shadow-sm',
          parentActive && !itemIsActive && 'bg-gray-50',
          collapsed && 'justify-center px-2'
        )}
        whileHover={{ x: level === 0 ? 2 : 1 }}
        whileTap={{ scale: 0.98 }}
      >
        <div className="flex items-center gap-3 min-w-0 flex-1">
          <motion.div
            className={cn(
              'flex-shrink-0 transition-colors duration-200',
              itemIsActive ? 'text-primary-600' : 'text-gray-500 group-hover:text-gray-700'
            )}
            whileHover={{ scale: 1.1 }}
          >
            {item.icon}
          </motion.div>
          
          <AnimatePresence>
            {!collapsed && (
              <motion.div
                initial={{ opacity: 0, width: 0 }}
                animate={{ opacity: 1, width: 'auto' }}
                exit={{ opacity: 0, width: 0 }}
                className="flex items-center justify-between w-full min-w-0"
              >
                <span className={cn(
                  'font-medium text-sm truncate transition-colors duration-200',
                  itemIsActive ? 'text-primary-700' : 'text-gray-700 group-hover:text-gray-900'
                )}>
                  {item.label}
                </span>
                
                <div className="flex items-center gap-2 flex-shrink-0">
                  {item.badge && (
                    <motion.span
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className={cn(
                        'px-2 py-0.5 text-xs font-medium rounded-full',
                        itemIsActive 
                          ? 'bg-primary-100 text-primary-700' 
                          : 'bg-gray-100 text-gray-600'
                      )}
                    >
                      {item.badge}
                    </motion.span>
                  )}
                  
                  {item.comingSoon && (
                    <span className="px-2 py-0.5 text-xs font-medium bg-yellow-100 text-yellow-700 rounded-full">
                      Soon
                    </span>
                  )}
                  
                  {hasChildren && (
                    <motion.div
                      animate={{ rotate: isExpanded ? 90 : 0 }}
                      transition={{ duration: 0.2 }}
                      className="text-gray-400"
                    >
                      <ChevronRight className="w-4 h-4" />
                    </motion.div>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Active indicator */}
        {itemIsActive && (
          <motion.div
            layoutId="activeIndicator"
            className="absolute left-0 top-0 bottom-0 w-1 bg-primary-600 rounded-r-full"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ type: 'spring', stiffness: 400, damping: 30 }}
          />
        )}
      </motion.div>
    );

    return (
      <div key={item.id} className="w-full">
        {item.href ? (
          <NavLink to={item.href} className="block w-full">
            {itemContent}
          </NavLink>
        ) : (
          <button
            onClick={() => hasChildren && toggleExpanded(item.id)}
            className="block w-full text-left"
            disabled={item.comingSoon}
          >
            {itemContent}
          </button>
        )}

        {/* Children */}
        <AnimatePresence>
          {hasChildren && isExpanded && !collapsed && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <div className="py-1 space-y-1">
                {item.children?.map(child => renderNavItem(child, level + 1))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  };

  return (
    <motion.aside
      animate={{ width: collapsed ? 80 : 280 }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
      className="bg-white border-r border-gray-200 flex flex-col h-full relative z-10"
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <motion.div
          className="flex items-center gap-3"
          animate={{ justifyContent: collapsed ? 'center' : 'flex-start' }}
        >
          <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
            <Store className="w-5 h-5 text-white" />
          </div>
          
          <AnimatePresence>
            {!collapsed && (
              <motion.div
                initial={{ opacity: 0, width: 0 }}
                animate={{ opacity: 1, width: 'auto' }}
                exit={{ opacity: 0, width: 0 }}
                className="overflow-hidden"
              >
                <h1 className="text-xl font-bold text-gray-900">WhiskAffair</h1>
                <p className="text-xs text-gray-500">Admin Dashboard</p>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
        {navigationItems.map(item => renderNavItem(item))}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <motion.div
          className={cn(
            'flex items-center gap-3 p-3 rounded-lg bg-gray-50',
            collapsed && 'justify-center'
          )}
        >
          <HelpCircle className="w-5 h-5 text-gray-500" />
          <AnimatePresence>
            {!collapsed && (
              <motion.div
                initial={{ opacity: 0, width: 0 }}
                animate={{ opacity: 1, width: 'auto' }}
                exit={{ opacity: 0, width: 0 }}
                className="overflow-hidden"
              >
                <p className="text-sm font-medium text-gray-700">Need help?</p>
                <p className="text-xs text-gray-500">Contact support</p>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>
    </motion.aside>
  );
};

export default AdminSidebar;
