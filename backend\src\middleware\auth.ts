import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { supabase } from '@/config/supabase';
import { User } from '@/types/supabase';
import { config } from '../config/environment';
import { logger } from '../config/logger';
import { AppError } from './errorHandler';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: Omit<User, 'password'>;
    }
  }
}

// JWT payload interface
interface JWTPayload {
  userId: string;
  role: 'admin' | 'customer';
  iat: number;
  exp: number;
}

// Authentication middleware
export const authMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.startsWith('Bearer ') 
      ? authHeader.substring(7) 
      : null;

    if (!token) {
      throw new AppError('Access token is required', 401);
    }

    // Verify token
    const decoded = jwt.verify(token, config.jwt.secret) as JWTPayload;

    // Get user from database
    const { data: user, error } = await supabase.getAdminClient()
      .from('users')
      .select('*')
      .eq('id', decoded.userId)
      .single();

    if (error || !user) {
      throw new AppError('User not found', 401);
    }

    // Check if user is active
    if (user.status !== 'active') {
      throw new AppError('Account is not active', 401);
    }

    // Attach user to request
    req.user = user;

    // Log access for security monitoring
    await supabase.trackEvent({
      event_type: 'api_access',
      user_id: user.id,
      session_id: req.sessionID || 'unknown',
      metadata: {
        endpoint: req.path,
        method: req.method,
        userAgent: req.get('User-Agent'),
        ip: req.ip
      }
    });

    next();
  } catch (error: any) {
    logger.error('Authentication failed:', error);

    if (error.name === 'JsonWebTokenError') {
      return next(new AppError('Invalid token', 401));
    }

    if (error.name === 'TokenExpiredError') {
      return next(new AppError('Token expired', 401));
    }

    next(error);
  }
};

// Optional authentication middleware (doesn't fail if no token)
export const optionalAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.startsWith('Bearer ') 
      ? authHeader.substring(7) 
      : null;

    if (token) {
      const decoded = jwt.verify(token, config.jwt.secret) as JWTPayload;
      
      const { data: user } = await supabase.getAdminClient()
        .from('users')
        .select('*')
        .eq('id', decoded.userId)
        .single();

      if (user && user.status === 'active') {
        req.user = user;
      }
    }

    next();
  } catch (error) {
    // Silently continue without authentication
    next();
  }
};

// Role-based authorization middleware
export const requireRole = (...roles: ('admin' | 'customer')[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      return next(new AppError('Authentication required', 401));
    }

    if (!roles.includes(req.user.role)) {
      return next(new AppError('Insufficient permissions', 403));
    }

    next();
  };
};

// Admin authorization middleware
export const requireAdmin = requireRole('admin');

// Super admin authorization middleware (same as admin in simplified system)
export const requireSuperAdmin = requireRole('admin');

// Email verification middleware
export const requireEmailVerification = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  if (!req.user) {
    return next(new AppError('Authentication required', 401));
  }

  // Email verification check removed for simplified schema

  next();
};

// Account ownership middleware (for user-specific resources)
export const requireOwnership = (userIdParam: string = 'userId') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      return next(new AppError('Authentication required', 401));
    }

    const resourceUserId = req.params[userIdParam] || req.body[userIdParam];

    // Admins can access any resource
    if (req.user.role === 'admin') {
      return next();
    }

    // Users can only access their own resources
    if (req.user.id !== resourceUserId) {
      return next(new AppError('Access denied', 403));
    }

    next();
  };
};

// Rate limiting by user
export const userRateLimit = (maxRequests: number, windowMs: number) => {
  const requests = new Map<string, { count: number; resetTime: number }>();

  return (req: Request, res: Response, next: NextFunction): void => {
    const userId = req.user?.id || req.ip;
    const now = Date.now();

    // Clean up expired entries
    for (const [key, value] of requests.entries()) {
      if (now > value.resetTime) {
        requests.delete(key);
      }
    }

    const userRequests = requests.get(userId);

    if (!userRequests || now > userRequests.resetTime) {
      // First request or window expired
      requests.set(userId, {
        count: 1,
        resetTime: now + windowMs,
      });
      return next();
    }

    if (userRequests.count >= maxRequests) {
      const remainingTime = Math.ceil((userRequests.resetTime - now) / 1000);
      return next(new AppError(
        `Rate limit exceeded. Try again in ${remainingTime} seconds.`,
        429
      ));
    }

    userRequests.count++;
    next();
  };
};

// API key authentication (for external integrations)
export const apiKeyAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const apiKey = req.headers['x-api-key'] as string;

    if (!apiKey) {
      throw new AppError('API key is required', 401);
    }

    // In a real implementation, you would validate the API key against a database
    // For now, we'll use a simple check
    const validApiKeys = process.env.VALID_API_KEYS?.split(',') || [];

    if (!validApiKeys.includes(apiKey)) {
      throw new AppError('Invalid API key', 401);
    }

    // Log API access
    logger.info('API key access:', {
      apiKey: apiKey.substring(0, 8) + '...',
      endpoint: req.path,
      method: req.method,
      ip: req.ip,
    });

    next();
  } catch (error) {
    next(error);
  }
};

// Session validation middleware
export const validateSession = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      return next();
    }

    // Simplified security monitoring for Supabase
    // Track security events through analytics
    await supabase.trackEvent({
      event_type: 'security_check',
      user_id: req.user.id,
      session_id: req.sessionID || 'unknown',
      metadata: {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        endpoint: req.path
      }
    });

    next();
  } catch (error) {
    logger.error('Session validation failed:', error);
    next();
  }
};

// Alias for backward compatibility
export const authenticateToken = authMiddleware;

export default authMiddleware;
