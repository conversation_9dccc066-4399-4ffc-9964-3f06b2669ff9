import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Gift, Package, Heart, Star, Truck, Shield } from 'lucide-react';

const GiftingExperience: React.FC = () => {
  const [selectedGiftBox, setSelectedGiftBox] = useState('premium');

  const giftBoxes = [
    {
      id: 'essential',
      name: 'Essential Collection',
      price: '₹899',
      items: 12,
      description: 'Perfect for small celebrations',
      features: ['12 Assorted Cookies', 'Elegant Box', 'Personalized Note'],
    },
    {
      id: 'premium',
      name: 'Premium Selection',
      price: '₹1,599',
      items: 24,
      description: 'Our most popular gift option',
      features: ['24 Premium Treats', 'Luxury Packaging', 'Custom Ribbon', 'Gift Card'],
      popular: true,
    },
    {
      id: 'corporate',
      name: 'Corporate Hamper',
      price: '₹2,499',
      items: 36,
      description: 'Ideal for business gifting',
      features: ['36 Gourmet Items', 'Executive Packaging', 'Company Branding', 'Bulk Discounts'],
    },
  ];

  const giftingFeatures = [
    {
      icon: Package,
      title: 'Luxury Packaging',
      description: 'Beautifully crafted boxes with golden ribbon and personalized cards',
    },
    {
      icon: Truck,
      title: 'Express Delivery',
      description: 'Same-day delivery across Mumbai, nationwide shipping available',
    },
    {
      icon: Shield,
      title: 'Freshness Guarantee',
      description: 'All products guaranteed fresh with extended shelf life',
    },
    {
      icon: Heart,
      title: 'Personal Touch',
      description: 'Custom messages and special occasion packaging options',
    },
  ];

  return (
    <section id="gifting" className="py-20 bg-gradient-to-br from-blush via-cream to-gold-light">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="font-playfair text-4xl md:text-5xl font-bold text-taupe mb-6">
            The Art of
            <span className="block text-gold">Gifting</span>
          </h2>
          <p className="font-montserrat text-lg text-taupe/80 max-w-2xl mx-auto">
            Transform any occasion into a memorable moment with our curated gift collections, 
            beautifully packaged and delivered with love.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-start">
          {/* Gift Box Builder */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h3 className="font-playfair text-3xl font-bold text-taupe mb-8">
              Create Your Perfect Gift
            </h3>

            <div className="space-y-6">
              {giftBoxes.map((box) => (
                <div
                  key={box.id}
                  className={`relative p-6 rounded-2xl border-2 transition-all duration-300 cursor-pointer ${
                    selectedGiftBox === box.id
                      ? 'border-gold bg-gold/10 shadow-gold'
                      : 'border-gold/20 bg-white/80 hover:border-gold/40'
                  }`}
                  onClick={() => setSelectedGiftBox(box.id)}
                >
                  {box.popular && (
                    <div className="absolute -top-3 left-6 bg-gold text-taupe px-4 py-1 rounded-full text-sm font-montserrat font-semibold">
                      Most Popular
                    </div>
                  )}
                  
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h4 className="font-playfair text-xl font-semibold text-taupe mb-1">
                        {box.name}
                      </h4>
                      <p className="font-montserrat text-taupe/60 text-sm">
                        {box.description}
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="font-playfair text-2xl font-bold text-taupe">
                        {box.price}
                      </div>
                      <div className="font-montserrat text-sm text-taupe/60">
                        {box.items} items
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2">
                    {box.features.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-gold rounded-full"></div>
                        <span className="font-montserrat text-sm text-taupe/70">
                          {feature}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              viewport={{ once: true }}
              className="mt-8"
            >
              <button className="w-full bg-gradient-gold text-taupe py-4 rounded-2xl font-montserrat font-bold hover:shadow-gold transition-all duration-300 flex items-center justify-center space-x-2">
                <Gift className="w-5 h-5" />
                <span>Customize & Order Gift Box</span>
              </button>
              
              <p className="font-montserrat text-sm text-taupe/60 text-center mt-3">
                Free personalization • Express delivery available
              </p>
            </motion.div>
          </motion.div>

          {/* Gift Box Visualizer */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative"
          >
            {/* 3D Gift Box Placeholder */}
            <div className="aspect-square bg-white rounded-3xl shadow-soft p-8 flex items-center justify-center relative overflow-hidden">
              <div className="text-center">
                <div className="w-48 h-48 bg-gradient-gold rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-gold">
                  <Package className="w-24 h-24 text-taupe" />
                </div>
                <h4 className="font-playfair text-2xl font-bold text-taupe mb-2">
                  {giftBoxes.find(box => box.id === selectedGiftBox)?.name}
                </h4>
                <div className="flex items-center justify-center space-x-1 mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 text-gold fill-current" />
                  ))}
                  <span className="font-montserrat text-sm text-taupe/60 ml-2">
                    (4.9/5)
                  </span>
                </div>
                <p className="font-montserrat text-taupe/70">
                  Interactive 3D preview coming soon
                </p>
              </div>
              
              {/* Floating elements */}
              <div className="absolute top-4 right-4 w-8 h-8 bg-gold/20 rounded-full animate-float"></div>
              <div className="absolute bottom-8 left-4 w-6 h-6 bg-blush/30 rounded-full animate-float" style={{ animationDelay: '2s' }}></div>
            </div>

            {/* Gifting Features */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              viewport={{ once: true }}
              className="mt-8 grid grid-cols-2 gap-4"
            >
              {giftingFeatures.map((feature, index) => (
                <div key={index} className="bg-white/80 p-4 rounded-xl backdrop-blur-sm">
                  <div className="w-10 h-10 bg-gold/20 rounded-xl flex items-center justify-center mb-3">
                    <feature.icon className="w-5 h-5 text-gold" />
                  </div>
                  <h5 className="font-montserrat font-semibold text-taupe text-sm mb-1">
                    {feature.title}
                  </h5>
                  <p className="font-montserrat text-xs text-taupe/60 leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              ))}
            </motion.div>
          </motion.div>
        </div>

        {/* Corporate Gifting CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-16 bg-white/60 backdrop-blur-sm rounded-3xl p-8 text-center border border-gold/20"
        >
          <h3 className="font-playfair text-2xl font-bold text-taupe mb-4">
            Corporate & Bulk Orders
          </h3>
          <p className="font-montserrat text-taupe/70 mb-6 max-w-2xl mx-auto">
            Celebrate your team, clients, and partners with premium gift hampers. 
            Custom branding and volume discounts available.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="px-6 py-3 bg-taupe text-cream rounded-full font-montserrat font-semibold hover:bg-taupe/90 transition-all duration-300">
              Request Corporate Quote
            </button>
            <button className="px-6 py-3 border-2 border-taupe text-taupe rounded-full font-montserrat font-semibold hover:bg-taupe hover:text-cream transition-all duration-300">
              View Bulk Pricing
            </button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default GiftingExperience;