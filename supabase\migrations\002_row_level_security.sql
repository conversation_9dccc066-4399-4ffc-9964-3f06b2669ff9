-- Row Level Security (RLS) Policies for WhiskAffair
-- Professional security setup with proper access controls

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE refresh_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_variants ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE cart_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE wishlist_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE pages ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;

-- ===== USER POLICIES =====

-- Users can read their own data
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.uid() = id);

-- Users can update their own data
CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

-- Admins can view all users
CREATE POLICY "Admins can view all users" ON users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('SUPER_ADMIN', 'PRODUCT_MANAGER')
        )
    );

-- ===== ADDRESS POLICIES =====

-- Users can manage their own addresses
CREATE POLICY "Users can manage own addresses" ON addresses
    FOR ALL USING (auth.uid() = user_id);

-- ===== PRODUCT POLICIES =====

-- Everyone can view active products
CREATE POLICY "Anyone can view active products" ON products
    FOR SELECT USING (status = 'ACTIVE');

-- Admins can manage all products
CREATE POLICY "Admins can manage products" ON products
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('SUPER_ADMIN', 'PRODUCT_MANAGER', 'CONTENT_EDITOR')
        )
    );

-- Everyone can view categories
CREATE POLICY "Anyone can view categories" ON categories
    FOR SELECT USING (is_active = true);

-- Admins can manage categories
CREATE POLICY "Admins can manage categories" ON categories
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('SUPER_ADMIN', 'PRODUCT_MANAGER', 'CONTENT_EDITOR')
        )
    );

-- Product images follow product visibility
CREATE POLICY "Product images follow product visibility" ON product_images
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM products 
            WHERE id = product_id 
            AND status = 'ACTIVE'
        )
    );

-- Product variants follow product visibility
CREATE POLICY "Product variants follow product visibility" ON product_variants
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM products 
            WHERE id = product_id 
            AND status = 'ACTIVE'
        )
    );

-- Product inventory follows product visibility
CREATE POLICY "Product inventory follows product visibility" ON product_inventory
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM products 
            WHERE id = product_id 
            AND status = 'ACTIVE'
        )
    );

-- ===== ORDER POLICIES =====

-- Users can view their own orders
CREATE POLICY "Users can view own orders" ON orders
    FOR SELECT USING (auth.uid() = user_id);

-- Users can create orders
CREATE POLICY "Users can create orders" ON orders
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Admins can view all orders
CREATE POLICY "Admins can view all orders" ON orders
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('SUPER_ADMIN', 'PRODUCT_MANAGER')
        )
    );

-- Order items follow order visibility
CREATE POLICY "Order items follow order visibility" ON order_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM orders 
            WHERE id = order_id 
            AND (user_id = auth.uid() OR EXISTS (
                SELECT 1 FROM users 
                WHERE id = auth.uid() 
                AND role IN ('SUPER_ADMIN', 'PRODUCT_MANAGER')
            ))
        )
    );

-- Payments follow order visibility
CREATE POLICY "Payments follow order visibility" ON payments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM orders 
            WHERE id = order_id 
            AND (user_id = auth.uid() OR EXISTS (
                SELECT 1 FROM users 
                WHERE id = auth.uid() 
                AND role IN ('SUPER_ADMIN', 'PRODUCT_MANAGER')
            ))
        )
    );

-- ===== CART & WISHLIST POLICIES =====

-- Users can manage their own cart
CREATE POLICY "Users can manage own cart" ON cart_items
    FOR ALL USING (auth.uid() = user_id);

-- Users can manage their own wishlist
CREATE POLICY "Users can manage own wishlist" ON wishlist_items
    FOR ALL USING (auth.uid() = user_id);

-- ===== REVIEW POLICIES =====

-- Anyone can read reviews
CREATE POLICY "Anyone can read reviews" ON reviews
    FOR SELECT USING (true);

-- Users can create reviews
CREATE POLICY "Users can create reviews" ON reviews
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own reviews
CREATE POLICY "Users can update own reviews" ON reviews
    FOR UPDATE USING (auth.uid() = user_id);

-- ===== ANALYTICS POLICIES =====

-- Analytics events can be inserted by anyone (for tracking)
CREATE POLICY "Anyone can insert analytics events" ON analytics_events
    FOR INSERT WITH CHECK (true);

-- Only admins can read analytics
CREATE POLICY "Admins can read analytics" ON analytics_events
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('SUPER_ADMIN', 'PRODUCT_MANAGER')
        )
    );

-- ===== SECURITY LOG POLICIES =====

-- Only admins can read security logs
CREATE POLICY "Admins can read security logs" ON security_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role = 'SUPER_ADMIN'
        )
    );

-- Security logs can be inserted by system
CREATE POLICY "System can insert security logs" ON security_logs
    FOR INSERT WITH CHECK (true);

-- ===== CONTENT POLICIES =====

-- Anyone can read published pages
CREATE POLICY "Anyone can read published pages" ON pages
    FOR SELECT USING (status = 'PUBLISHED');

-- Content editors can manage pages
CREATE POLICY "Content editors can manage pages" ON pages
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('SUPER_ADMIN', 'CONTENT_EDITOR')
        )
    );

-- Anyone can read published blog posts
CREATE POLICY "Anyone can read published blog posts" ON blog_posts
    FOR SELECT USING (status = 'PUBLISHED');

-- Content editors can manage blog posts
CREATE POLICY "Content editors can manage blog posts" ON blog_posts
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('SUPER_ADMIN', 'CONTENT_EDITOR')
        )
    );

-- ===== REFRESH TOKEN POLICIES =====

-- Users can manage their own refresh tokens
CREATE POLICY "Users can manage own refresh tokens" ON refresh_tokens
    FOR ALL USING (auth.uid() = user_id);

-- ===== FUNCTIONS FOR COMMON CHECKS =====

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM users 
        WHERE id = user_id 
        AND role IN ('SUPER_ADMIN', 'PRODUCT_MANAGER', 'CONTENT_EDITOR')
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is super admin
CREATE OR REPLACE FUNCTION is_super_admin(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM users 
        WHERE id = user_id 
        AND role = 'SUPER_ADMIN'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user role
CREATE OR REPLACE FUNCTION get_user_role(user_id UUID)
RETURNS user_role AS $$
DECLARE
    user_role_result user_role;
BEGIN
    SELECT role INTO user_role_result 
    FROM users 
    WHERE id = user_id;
    
    RETURN user_role_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
