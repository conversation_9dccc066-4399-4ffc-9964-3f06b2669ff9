import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'super_admin' | 'product_manager' | 'content_editor';
  permissions: string[];
  avatar?: string;
  lastLogin?: string;
  twoFactorEnabled: boolean;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  setUser: (user: User) => void;
  setToken: (token: string) => void;
  verifyTwoFactor: (code: string) => Promise<void>;
}

// Demo users for testing
const demoUsers = {
  '<EMAIL>': {
    password: 'admin123',
    user: {
      id: '1',
      email: '<EMAIL>',
      name: 'Super Admin',
      role: 'super_admin' as const,
      permissions: [
        'products.read', 'products.write', 'products.delete',
        'orders.read', 'orders.write', 'orders.delete',
        'users.read', 'users.write', 'users.delete',
        'content.read', 'content.write', 'content.delete',
        'system.read', 'system.write', 'system.delete'
      ],
      lastLogin: '2024-01-15 09:30 AM',
      twoFactorEnabled: false,
    }
  },
  '<EMAIL>': {
    password: 'manager123',
    user: {
      id: '2',
      email: '<EMAIL>',
      name: 'Product Manager',
      role: 'product_manager' as const,
      permissions: [
        'products.read', 'products.write',
        'orders.read', 'orders.write',
        'users.read',
        'content.read'
      ],
      lastLogin: '2024-01-15 08:45 AM',
      twoFactorEnabled: false,
    }
  },
  '<EMAIL>': {
    password: 'editor123',
    user: {
      id: '3',
      email: '<EMAIL>',
      name: 'Content Editor',
      role: 'content_editor' as const,
      permissions: [
        'content.read', 'content.write',
        'products.read',
        'orders.read'
      ],
      lastLogin: '2024-01-15 10:15 AM',
      twoFactorEnabled: false,
    }
  }
};

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,

      login: async (email: string, password: string) => {
        set({ isLoading: true });
        
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        try {
          const demoUser = demoUsers[email as keyof typeof demoUsers];
          
          if (!demoUser || demoUser.password !== password) {
            throw new Error('Invalid credentials');
          }
          
          // Simulate token generation
          const token = `demo_token_${Date.now()}`;
          
          set({
            user: demoUser.user,
            token,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
        });
        localStorage.removeItem('auth-storage');
      },

      setUser: (user: User) => set({ user }),
      setToken: (token: string) => set({ token }),

      verifyTwoFactor: async (code: string) => {
        set({ isLoading: true });
        
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        try {
          // For demo, accept any 6-digit code
          if (!/^\d{6}$/.test(code)) {
            throw new Error('Invalid 2FA code format');
          }
          
          // Simulate successful 2FA verification
          const currentUser = get().user;
          if (currentUser) {
            set({
              isAuthenticated: true,
              isLoading: false,
            });
          }
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);