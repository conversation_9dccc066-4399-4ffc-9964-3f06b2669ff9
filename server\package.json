{"name": "whisk-affair-backend", "version": "1.0.0", "description": "WhiskAffair E-commerce Backend API with Analytics and Security", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:types": "supabase gen types typescript --project-id YOUR_PROJECT_ID --schema public > src/types/supabase.ts", "db:reset": "supabase db reset", "db:seed": "ts-node src/database/seed.ts"}, "keywords": ["ecommerce", "api", "analytics", "security", "typescript", "express"], "author": "WhiskAffair Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "speakeasy": "^2.0.0", "qrcode": "^1.5.3", "nodemailer": "^6.9.7", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "winston": "^3.11.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "@supabase/supabase-js": "^2.38.4", "redis": "^4.6.10", "socket.io": "^4.7.4", "stripe": "^14.7.0", "axios": "^1.6.2", "joi": "^17.11.0", "uuid": "^9.0.1", "crypto-js": "^4.2.0", "express-session": "^1.17.3", "connect-redis": "^7.1.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/speakeasy": "^2.0.10", "@types/qrcode": "^1.5.5", "@types/nodemailer": "^6.4.14", "@types/multer": "^1.4.11", "@types/morgan": "^1.9.9", "@types/uuid": "^9.0.7", "@types/crypto-js": "^4.2.1", "@types/express-session": "^1.17.10", "@types/passport": "^1.0.16", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/node": "^20.9.0", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.2.2", "ts-node": "^10.9.1", "nodemon": "^3.0.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}