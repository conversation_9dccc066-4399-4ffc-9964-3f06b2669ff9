const axios = require('axios');

const BASE_URL = 'http://localhost:3001';
const API_BASE = `${BASE_URL}/api/v1`;

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  firstName: 'Test',
  lastName: 'User',
  role: 'customer'
};

const testProduct = {
  name: 'Test Whisk Set',
  description: 'A premium whisk set for professional cooking',
  price: 49.99,
  category: 'Kitchen Tools',
  images: ['https://example.com/whisk1.jpg'],
  inStock: true,
  featured: true
};

const testCategory = {
  name: 'Kitchen Tools',
  description: 'Professional kitchen tools and utensils',
  slug: 'kitchen-tools'
};

// Helper function to log test results
function logTest(testName, success, data = null, error = null) {
  const status = success ? '✅ PASS' : '❌ FAIL';
  console.log(`${status} ${testName}`);
  if (data && success) {
    console.log(`   Response: ${JSON.stringify(data).substring(0, 100)}...`);
  }
  if (error && !success) {
    console.log(`   Error: ${error.message || error}`);
  }
  console.log('');
}

// Test functions
async function testHealthCheck() {
  try {
    const response = await axios.get(`${API_BASE}/health`);
    logTest('Health Check', response.status === 200, response.data);
    return true;
  } catch (error) {
    logTest('Health Check', false, null, error);
    return false;
  }
}

async function testRootEndpoint() {
  try {
    const response = await axios.get(BASE_URL);
    logTest('Root Endpoint', response.status === 200, response.data);
    return true;
  } catch (error) {
    logTest('Root Endpoint', false, null, error);
    return false;
  }
}

async function testAPIDocumentation() {
  try {
    const response = await axios.get(`${API_BASE}/docs`);
    logTest('API Documentation', response.status === 200, response.data);
    return true;
  } catch (error) {
    logTest('API Documentation', false, null, error);
    return false;
  }
}

async function testUserRegistration() {
  try {
    const response = await axios.post(`${API_BASE}/auth/register`, testUser);
    logTest('User Registration', response.status === 201, response.data);
    return response.data;
  } catch (error) {
    logTest('User Registration', false, null, error);
    return null;
  }
}

async function testUserLogin() {
  try {
    const response = await axios.post(`${API_BASE}/auth/login`, {
      email: testUser.email,
      password: testUser.password
    });
    logTest('User Login', response.status === 200, response.data);
    return response.data;
  } catch (error) {
    logTest('User Login', false, null, error);
    return null;
  }
}

async function testCreateCategory(token) {
  try {
    const response = await axios.post(`${API_BASE}/categories`, testCategory, {
      headers: { Authorization: `Bearer ${token}` }
    });
    logTest('Create Category', response.status === 201, response.data);
    return response.data;
  } catch (error) {
    logTest('Create Category', false, null, error);
    return null;
  }
}

async function testGetCategories() {
  try {
    const response = await axios.get(`${API_BASE}/categories`);
    logTest('Get Categories', response.status === 200, response.data);
    return response.data;
  } catch (error) {
    logTest('Get Categories', false, null, error);
    return null;
  }
}

async function testCreateProduct(token, categoryId) {
  try {
    const productData = { ...testProduct, categoryId };
    const response = await axios.post(`${API_BASE}/products`, productData, {
      headers: { Authorization: `Bearer ${token}` }
    });
    logTest('Create Product', response.status === 201, response.data);
    return response.data;
  } catch (error) {
    logTest('Create Product', false, null, error);
    return null;
  }
}

async function testGetProducts() {
  try {
    const response = await axios.get(`${API_BASE}/products`);
    logTest('Get Products', response.status === 200, response.data);
    return response.data;
  } catch (error) {
    logTest('Get Products', false, null, error);
    return null;
  }
}

async function testAnalyticsTracking() {
  try {
    const response = await axios.post(`${API_BASE}/analytics/track`, {
      eventType: 'page_view',
      page: '/products',
      sessionId: 'test-session-123'
    });
    logTest('Analytics Tracking', response.status === 200, response.data);
    return response.data;
  } catch (error) {
    logTest('Analytics Tracking', false, null, error);
    return null;
  }
}

async function testCMSPages() {
  try {
    const response = await axios.get(`${API_BASE}/cms/pages`);
    logTest('Get CMS Pages', response.status === 200, response.data);
    return response.data;
  } catch (error) {
    logTest('Get CMS Pages', false, null, error);
    return null;
  }
}

// Main test runner
async function runAllTests() {
  console.log('🧪 Starting WhiskAffair Backend Tests...\n');
  console.log('=' .repeat(50));
  
  // Basic connectivity tests
  console.log('📡 CONNECTIVITY TESTS');
  console.log('-'.repeat(30));
  
  const healthOk = await testHealthCheck();
  if (!healthOk) {
    console.log('❌ Server is not responding. Please check if the server is running on port 3001.');
    return;
  }
  
  await testRootEndpoint();
  await testAPIDocumentation();
  
  // Authentication tests
  console.log('🔐 AUTHENTICATION TESTS');
  console.log('-'.repeat(30));
  
  const registrationResult = await testUserRegistration();
  const loginResult = await testUserLogin();
  
  const token = loginResult?.token || loginResult?.accessToken;
  
  // Data management tests
  console.log('📊 DATA MANAGEMENT TESTS');
  console.log('-'.repeat(30));
  
  const categoryResult = await testCreateCategory(token);
  await testGetCategories();
  
  const categoryId = categoryResult?.id || categoryResult?.category?.id;
  const productResult = await testCreateProduct(token, categoryId);
  await testGetProducts();
  
  // Analytics tests
  console.log('📈 ANALYTICS TESTS');
  console.log('-'.repeat(30));
  
  await testAnalyticsTracking();
  
  // CMS tests
  console.log('📝 CMS TESTS');
  console.log('-'.repeat(30));
  
  await testCMSPages();
  
  console.log('=' .repeat(50));
  console.log('🎉 Backend testing completed!');
}

// Run tests
runAllTests().catch(error => {
  console.error('Test runner failed:', error);
});
