import { supabase } from '@/config/supabase';
import { Product, Category, Inserts, Updates } from '@/types/supabase';

export class ProductService {
  // Product CRUD operations
  async getAllProducts(filters?: {
    category_id?: string;
    is_active?: boolean;
    is_featured?: boolean;
    search?: string;
    limit?: number;
    offset?: number;
  }) {
    let query = supabase.getAdminClient()
      .from('products')
      .select(`
        *,
        categories (
          id,
          name,
          slug
        )
      `);

    if (filters?.category_id) {
      query = query.eq('category_id', filters.category_id);
    }

    if (filters?.is_active !== undefined) {
      query = query.eq('is_active', filters.is_active);
    }

    if (filters?.is_featured !== undefined) {
      query = query.eq('is_featured', filters.is_featured);
    }

    if (filters?.search) {
      query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
    }

    if (filters?.limit) {
      query = query.limit(filters.limit);
    }

    if (filters?.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
    }

    query = query.order('created_at', { ascending: false });

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch products: ${error.message}`);
    }

    return data;
  }

  async getProductById(id: string) {
    const { data, error } = await supabase.getAdminClient()
      .from('products')
      .select(`
        *,
        categories (
          id,
          name,
          slug
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      throw new Error(`Failed to fetch product: ${error.message}`);
    }

    return data;
  }

  async getProductBySlug(slug: string) {
    const { data, error } = await supabase.getAdminClient()
      .from('products')
      .select(`
        *,
        categories (
          id,
          name,
          slug
        )
      `)
      .eq('slug', slug)
      .eq('is_active', true)
      .single();

    if (error) {
      throw new Error(`Failed to fetch product: ${error.message}`);
    }

    return data;
  }

  async createProduct(productData: Inserts<'products'>) {
    const { data, error } = await supabase.getAdminClient()
      .from('products')
      .insert(productData)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create product: ${error.message}`);
    }

    return data;
  }

  async updateProduct(id: string, productData: Updates<'products'>) {
    const { data, error } = await supabase.getAdminClient()
      .from('products')
      .update({ ...productData, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update product: ${error.message}`);
    }

    return data;
  }

  async deleteProduct(id: string) {
    const { error } = await supabase.getAdminClient()
      .from('products')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to delete product: ${error.message}`);
    }

    return true;
  }

  // Category CRUD operations
  async getAllCategories(filters?: {
    is_active?: boolean;
    limit?: number;
    offset?: number;
  }) {
    let query = supabase.getAdminClient()
      .from('categories')
      .select('*');

    if (filters?.is_active !== undefined) {
      query = query.eq('is_active', filters.is_active);
    }

    if (filters?.limit) {
      query = query.limit(filters.limit);
    }

    if (filters?.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
    }

    query = query.order('sort_order', { ascending: true });

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch categories: ${error.message}`);
    }

    return data;
  }

  async getCategoryById(id: string) {
    const { data, error } = await supabase.getAdminClient()
      .from('categories')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      throw new Error(`Failed to fetch category: ${error.message}`);
    }

    return data;
  }

  async createCategory(categoryData: Inserts<'categories'>) {
    const { data, error } = await supabase.getAdminClient()
      .from('categories')
      .insert(categoryData)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create category: ${error.message}`);
    }

    return data;
  }

  async updateCategory(id: string, categoryData: Updates<'categories'>) {
    const { data, error } = await supabase.getAdminClient()
      .from('categories')
      .update({ ...categoryData, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update category: ${error.message}`);
    }

    return data;
  }

  async deleteCategory(id: string) {
    // Check if category has products
    const { data: products } = await supabase.getAdminClient()
      .from('products')
      .select('id')
      .eq('category_id', id)
      .limit(1);

    if (products && products.length > 0) {
      throw new Error('Cannot delete category with existing products');
    }

    const { error } = await supabase.getAdminClient()
      .from('categories')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to delete category: ${error.message}`);
    }

    return true;
  }

  // Featured products
  async getFeaturedProducts(limit: number = 8) {
    const { data, error } = await supabase.getAdminClient()
      .from('products')
      .select(`
        *,
        categories (
          id,
          name,
          slug
        )
      `)
      .eq('is_active', true)
      .eq('is_featured', true)
      .limit(limit)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch featured products: ${error.message}`);
    }

    return data;
  }

  // Product search
  async searchProducts(query: string, limit: number = 20) {
    const { data, error } = await supabase.getAdminClient()
      .getAdminClient().from('products')
      .select(`
        *,
        categories (
          id,
          name,
          slug
        )
      `)
      .eq('is_active', true)
      .or(`name.ilike.%${query}%,description.ilike.%${query}%,short_description.ilike.%${query}%`)
      .limit(limit)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to search products: ${error.message}`);
    }

    return data;
  }
}

export const productService = new ProductService();
