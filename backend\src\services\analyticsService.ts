import { supabase } from '@/config/supabase';
import { redis } from '@/config/redis';
import { logger, logAnalytics } from '@/config/logger';
import { config } from '@/config/environment';

export interface AnalyticsEvent {
  eventType: string;
  userId?: string;
  sessionId: string;
  productId?: string;
  properties: Record<string, any>;
  page?: string;
  referrer?: string;
  userAgent?: string;
  ipAddress?: string;
  timestamp?: string;
}

export interface AnalyticsMetrics {
  totalUsers: number;
  activeUsers: number;
  pageViews: number;
  sessions: number;
  bounceRate: number;
  averageSessionDuration: number;
  conversionRate: number;
  revenue: number;
}

export interface ProductAnalytics {
  productId: string;
  views: number;
  clicks: number;
  addToCarts: number;
  purchases: number;
  revenue: number;
  conversionRate: number;
  averageTimeSpent: number;
}

export interface UserJourney {
  sessionId: string;
  userId?: string;
  events: AnalyticsEvent[];
  startTime: Date;
  endTime: Date;
  duration: number;
  pages: string[];
  conversions: number;
}

export class AnalyticsService {
  private eventBuffer: AnalyticsEvent[] = [];
  private flushTimer: NodeJS.Timeout | null = null;

  constructor() {
    this.startFlushTimer();
  }

  // Start the flush timer for batched processing
  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flushEvents();
    }, config.analytics.flushInterval);
  }

  // Track a single analytics event
  async trackEvent(event: AnalyticsEvent): Promise<void> {
    try {
      // Add timestamp if not provided
      if (!event.timestamp) {
        event.timestamp = new Date().toISOString();
      }

      // Validate event data
      this.validateEvent(event);

      // Add to buffer for batch processing
      this.eventBuffer.push(event);

      // Also store in Redis for real-time processing
      await redis.bufferAnalyticsEvent(event);

      // Log the event
      logAnalytics(event.eventType, event);

      // Flush if buffer is full
      if (this.eventBuffer.length >= config.analytics.batchSize) {
        await this.flushEvents();
      }
    } catch (error) {
      logger.error('Failed to track analytics event:', error);
      throw error;
    }
  }

  // Track multiple events in batch
  async trackEvents(events: AnalyticsEvent[]): Promise<void> {
    try {
      for (const event of events) {
        await this.trackEvent(event);
      }
    } catch (error) {
      logger.error('Failed to track analytics events batch:', error);
      throw error;
    }
  }

  // Flush buffered events to database
  private async flushEvents(): Promise<void> {
    if (this.eventBuffer.length === 0) return;

    try {
      const eventsToFlush = [...this.eventBuffer];
      this.eventBuffer = [];

      // Store events in database
      await supabase.batchInsert('analytics_events', eventsToFlush.map(event => ({
        event_type: event.eventType,
        user_id: event.userId,
        session_id: event.sessionId,
        product_id: event.productId,
        properties: event.properties,
        page: event.page,
        referrer: event.referrer,
        user_agent: event.userAgent,
        ip_address: event.ipAddress,
        created_at: event.timestamp ? new Date(event.timestamp).toISOString() : new Date().toISOString(),
      })));

      logger.info(`Flushed ${eventsToFlush.length} analytics events to database`);
    } catch (error) {
      logger.error('Failed to flush analytics events:', error);
      // Put events back in buffer for retry
      this.eventBuffer.unshift(...this.eventBuffer);
    }
  }

  // Validate event data
  private validateEvent(event: AnalyticsEvent): void {
    if (!event.eventType) {
      throw new Error('Event type is required');
    }

    if (!event.sessionId) {
      throw new Error('Session ID is required');
    }

    if (typeof event.properties !== 'object') {
      throw new Error('Properties must be an object');
    }

    // Validate event type
    const validEventTypes = [
      'page_view', 'product_view', 'product_click', 'add_to_cart', 'remove_from_cart',
      'checkout_start', 'checkout_step', 'purchase', 'search', 'scroll_depth',
      'video_play', 'form_interaction', 'newsletter_signup', 'social_share'
    ];

    if (!validEventTypes.includes(event.eventType)) {
      logger.warn(`Unknown event type: ${event.eventType}`);
    }
  }

  // Get analytics metrics for a date range
  async getMetrics(
    startDate: Date,
    endDate: Date,
    filters?: Record<string, any>
  ): Promise<AnalyticsMetrics> {
    try {
      const whereClause: any = {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      };

      // Apply filters
      if (filters) {
        if (filters.userId) whereClause.userId = filters.userId;
        if (filters.eventType) whereClause.eventType = filters.eventType;
        if (filters.productId) whereClause.productId = filters.productId;
      }

      // Get all events for the period
      const events = await db.analyticsEvent.findMany({
        where: whereClause,
        orderBy: { createdAt: 'asc' },
      });

      // Calculate metrics
      const pageViews = events.filter(e => e.eventType === 'page_view').length;
      const uniqueUsers = new Set(events.filter(e => e.userId).map(e => e.userId)).size;
      const uniqueSessions = new Set(events.map(e => e.sessionId)).size;
      const purchases = events.filter(e => e.eventType === 'purchase');
      
      // Calculate session durations
      const sessionDurations = this.calculateSessionDurations(events);
      const averageSessionDuration = sessionDurations.length > 0
        ? sessionDurations.reduce((sum, duration) => sum + duration, 0) / sessionDurations.length
        : 0;

      // Calculate bounce rate (sessions with only one page view)
      const sessionPageViews = new Map<string, number>();
      events.filter(e => e.eventType === 'page_view').forEach(e => {
        sessionPageViews.set(e.sessionId, (sessionPageViews.get(e.sessionId) || 0) + 1);
      });
      const bouncedSessions = Array.from(sessionPageViews.values()).filter(count => count === 1).length;
      const bounceRate = uniqueSessions > 0 ? bouncedSessions / uniqueSessions : 0;

      // Calculate conversion rate
      const sessionsWithPurchases = new Set(purchases.map(p => p.sessionId)).size;
      const conversionRate = uniqueSessions > 0 ? sessionsWithPurchases / uniqueSessions : 0;

      // Calculate revenue
      const revenue = purchases.reduce((sum, purchase) => {
        const amount = purchase.properties?.amount || purchase.properties?.value || 0;
        return sum + (typeof amount === 'number' ? amount : 0);
      }, 0);

      return {
        totalUsers: uniqueUsers,
        activeUsers: uniqueUsers, // For simplicity, treating all users as active
        pageViews,
        sessions: uniqueSessions,
        bounceRate,
        averageSessionDuration,
        conversionRate,
        revenue,
      };
    } catch (error) {
      logger.error('Failed to get analytics metrics:', error);
      throw error;
    }
  }

  // Get product analytics
  async getProductAnalytics(
    productId: string,
    startDate: Date,
    endDate: Date
  ): Promise<ProductAnalytics> {
    try {
      const events = await db.analyticsEvent.findMany({
        where: {
          productId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      const views = events.filter(e => e.eventType === 'product_view').length;
      const clicks = events.filter(e => e.eventType === 'product_click').length;
      const addToCarts = events.filter(e => e.eventType === 'add_to_cart').length;
      const purchases = events.filter(e => e.eventType === 'purchase').length;

      // Calculate revenue from purchases
      const revenue = events
        .filter(e => e.eventType === 'purchase')
        .reduce((sum, purchase) => {
          const amount = purchase.properties?.amount || purchase.properties?.value || 0;
          return sum + (typeof amount === 'number' ? amount : 0);
        }, 0);

      // Calculate conversion rate
      const conversionRate = views > 0 ? purchases / views : 0;

      // Calculate average time spent (from product_view events)
      const viewEvents = events.filter(e => e.eventType === 'product_view');
      const totalTimeSpent = viewEvents.reduce((sum, event) => {
        const timeSpent = event.properties?.timeSpent || 0;
        return sum + (typeof timeSpent === 'number' ? timeSpent : 0);
      }, 0);
      const averageTimeSpent = viewEvents.length > 0 ? totalTimeSpent / viewEvents.length : 0;

      return {
        productId,
        views,
        clicks,
        addToCarts,
        purchases,
        revenue,
        conversionRate,
        averageTimeSpent,
      };
    } catch (error) {
      logger.error('Failed to get product analytics:', error);
      throw error;
    }
  }

  // Get user journey for a session
  async getUserJourney(sessionId: string): Promise<UserJourney | null> {
    try {
      const events = await db.analyticsEvent.findMany({
        where: { sessionId },
        orderBy: { createdAt: 'asc' },
      });

      if (events.length === 0) {
        return null;
      }

      const startTime = events[0]!.createdAt;
      const endTime = events[events.length - 1]!.createdAt;
      const duration = endTime.getTime() - startTime.getTime();

      const pages = Array.from(new Set(
        events
          .filter(e => e.page)
          .map(e => e.page!)
      ));

      const conversions = events.filter(e => e.eventType === 'purchase').length;

      return {
        sessionId,
        userId: events.find(e => e.userId)?.userId,
        events: events.map(e => ({
          eventType: e.eventType,
          userId: e.userId || undefined,
          sessionId: e.sessionId,
          productId: e.productId || undefined,
          properties: e.properties as Record<string, any>,
          page: e.page || undefined,
          referrer: e.referrer || undefined,
          userAgent: e.userAgent || undefined,
          ipAddress: e.ipAddress || undefined,
          timestamp: e.createdAt.toISOString(),
        })),
        startTime,
        endTime,
        duration,
        pages,
        conversions,
      };
    } catch (error) {
      logger.error('Failed to get user journey:', error);
      throw error;
    }
  }

  // Calculate session durations from events
  private calculateSessionDurations(events: AnalyticsEvent[]): number[] {
    const sessionTimes = new Map<string, { start: Date; end: Date }>();

    events.forEach(event => {
      const timestamp = new Date(event.timestamp || Date.now());
      const existing = sessionTimes.get(event.sessionId);

      if (!existing) {
        sessionTimes.set(event.sessionId, { start: timestamp, end: timestamp });
      } else {
        if (timestamp < existing.start) existing.start = timestamp;
        if (timestamp > existing.end) existing.end = timestamp;
      }
    });

    return Array.from(sessionTimes.values()).map(
      ({ start, end }) => end.getTime() - start.getTime()
    );
  }

  // Get top pages by views
  async getTopPages(
    startDate: Date,
    endDate: Date,
    limit: number = 10
  ): Promise<Array<{ page: string; views: number; uniqueViews: number }>> {
    try {
      const events = await db.analyticsEvent.findMany({
        where: {
          eventType: 'page_view',
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
          page: { not: null },
        },
        select: {
          page: true,
          sessionId: true,
        },
      });

      const pageStats = new Map<string, { views: number; sessions: Set<string> }>();

      events.forEach(event => {
        if (!event.page) return;

        const existing = pageStats.get(event.page) || { views: 0, sessions: new Set() };
        existing.views++;
        existing.sessions.add(event.sessionId);
        pageStats.set(event.page, existing);
      });

      return Array.from(pageStats.entries())
        .map(([page, stats]) => ({
          page,
          views: stats.views,
          uniqueViews: stats.sessions.size,
        }))
        .sort((a, b) => b.views - a.views)
        .slice(0, limit);
    } catch (error) {
      logger.error('Failed to get top pages:', error);
      throw error;
    }
  }

  // Get real-time analytics data
  async getRealTimeData(): Promise<{
    activeUsers: number;
    currentPageViews: number;
    recentEvents: AnalyticsEvent[];
  }> {
    try {
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);

      const recentEvents = await db.analyticsEvent.findMany({
        where: {
          createdAt: { gte: fiveMinutesAgo },
        },
        orderBy: { createdAt: 'desc' },
        take: 50,
      });

      const activeUsers = new Set(
        recentEvents.filter(e => e.userId).map(e => e.userId)
      ).size;

      const currentPageViews = recentEvents.filter(
        e => e.eventType === 'page_view'
      ).length;

      return {
        activeUsers,
        currentPageViews,
        recentEvents: recentEvents.map(e => ({
          eventType: e.eventType,
          userId: e.userId || undefined,
          sessionId: e.sessionId,
          productId: e.productId || undefined,
          properties: e.properties as Record<string, any>,
          page: e.page || undefined,
          referrer: e.referrer || undefined,
          userAgent: e.userAgent || undefined,
          ipAddress: e.ipAddress || undefined,
          timestamp: e.createdAt.toISOString(),
        })),
      };
    } catch (error) {
      logger.error('Failed to get real-time analytics data:', error);
      throw error;
    }
  }

  // Cleanup old events (for data retention)
  async cleanupOldEvents(retentionDays: number = 365): Promise<number> {
    try {
      const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000);

      const result = await db.analyticsEvent.deleteMany({
        where: {
          createdAt: { lt: cutoffDate },
        },
      });

      logger.info(`Cleaned up ${result.count} old analytics events`);
      return result.count;
    } catch (error) {
      logger.error('Failed to cleanup old analytics events:', error);
      throw error;
    }
  }

  // Graceful shutdown
  async shutdown(): Promise<void> {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }

    // Flush any remaining events
    await this.flushEvents();
  }
}

export const analyticsService = new AnalyticsService();

// Graceful shutdown handling
process.on('SIGTERM', () => analyticsService.shutdown());
process.on('SIGINT', () => analyticsService.shutdown());
