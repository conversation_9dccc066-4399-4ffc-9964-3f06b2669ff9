import React, { forwardRef } from 'react';
import { motion, HTMLMotionProps } from 'framer-motion';
import { Loader2 } from 'lucide-react';
import { cn } from '../../utils/cn';

// Professional Button Component - Shopify-level Quality
interface ButtonProps extends Omit<HTMLMotionProps<'button'>, 'size'> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive' | 'success';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  loading?: boolean;
  loadingText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
  children: React.ReactNode;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    variant = 'primary',
    size = 'md',
    loading = false,
    loadingText,
    leftIcon,
    rightIcon,
    fullWidth = false,
    disabled,
    className,
    children,
    ...props
  }, ref) => {
    const baseClasses = [
      // Base styles
      'inline-flex items-center justify-center',
      'font-medium transition-all duration-200',
      'focus:outline-none focus:ring-2 focus:ring-offset-2',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'relative overflow-hidden',
      
      // Professional micro-interactions
      'transform-gpu',
      'active:scale-[0.98]',
      'hover:shadow-lg',
      'transition-all duration-200 ease-out',
    ];

    // Variant styles - Professional color schemes
    const variantClasses = {
      primary: [
        'bg-primary-600 text-white',
        'hover:bg-primary-700 hover:shadow-primary-500/25',
        'focus:ring-primary-500',
        'active:bg-primary-800',
        'shadow-sm',
      ],
      secondary: [
        'bg-gray-100 text-gray-900',
        'hover:bg-gray-200 hover:shadow-gray-500/10',
        'focus:ring-gray-500',
        'active:bg-gray-300',
        'border border-gray-200',
      ],
      outline: [
        'bg-transparent text-gray-700 border-2 border-gray-300',
        'hover:bg-gray-50 hover:border-gray-400',
        'focus:ring-gray-500',
        'active:bg-gray-100',
      ],
      ghost: [
        'bg-transparent text-gray-700',
        'hover:bg-gray-100',
        'focus:ring-gray-500',
        'active:bg-gray-200',
      ],
      destructive: [
        'bg-error-600 text-white',
        'hover:bg-error-700 hover:shadow-error-500/25',
        'focus:ring-error-500',
        'active:bg-error-800',
        'shadow-sm',
      ],
      success: [
        'bg-success-600 text-white',
        'hover:bg-success-700 hover:shadow-success-500/25',
        'focus:ring-success-500',
        'active:bg-success-800',
        'shadow-sm',
      ],
    };

    // Size styles - Professional spacing
    const sizeClasses = {
      sm: [
        'h-8 px-3 text-sm',
        'rounded-md',
        'gap-1.5',
      ],
      md: [
        'h-10 px-4 text-sm',
        'rounded-lg',
        'gap-2',
      ],
      lg: [
        'h-12 px-6 text-base',
        'rounded-lg',
        'gap-2',
      ],
      xl: [
        'h-14 px-8 text-lg',
        'rounded-xl',
        'gap-3',
      ],
    };

    const widthClasses = fullWidth ? 'w-full' : '';

    const isDisabled = disabled || loading;

    return (
      <motion.button
        ref={ref}
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[size],
          widthClasses,
          className
        )}
        disabled={isDisabled}
        whileHover={!isDisabled ? { y: -1 } : undefined}
        whileTap={!isDisabled ? { scale: 0.98 } : undefined}
        transition={{
          type: 'spring',
          stiffness: 400,
          damping: 17,
        }}
        {...props}
      >
        {/* Loading state */}
        {loading && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="absolute inset-0 flex items-center justify-center bg-inherit rounded-inherit"
          >
            <Loader2 className="w-4 h-4 animate-spin" />
            {loadingText && (
              <span className="ml-2">{loadingText}</span>
            )}
          </motion.div>
        )}

        {/* Content */}
        <motion.div
          className={cn(
            'flex items-center justify-center gap-inherit',
            loading && 'opacity-0'
          )}
          animate={{ opacity: loading ? 0 : 1 }}
          transition={{ duration: 0.2 }}
        >
          {leftIcon && (
            <motion.span
              className="flex-shrink-0"
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
            >
              {leftIcon}
            </motion.span>
          )}
          
          <span className="truncate">{children}</span>
          
          {rightIcon && (
            <motion.span
              className="flex-shrink-0"
              initial={{ opacity: 0, x: 10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
            >
              {rightIcon}
            </motion.span>
          )}
        </motion.div>

        {/* Ripple effect */}
        <motion.div
          className="absolute inset-0 rounded-inherit"
          initial={{ scale: 0, opacity: 0.5 }}
          whileTap={{ scale: 1, opacity: 0 }}
          transition={{ duration: 0.3 }}
          style={{
            background: 'radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%)',
          }}
        />
      </motion.button>
    );
  }
);

Button.displayName = 'Button';

// Button Group Component for related actions
interface ButtonGroupProps {
  children: React.ReactNode;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
  spacing?: 'none' | 'sm' | 'md' | 'lg';
}

export const ButtonGroup: React.FC<ButtonGroupProps> = ({
  children,
  className,
  orientation = 'horizontal',
  spacing = 'sm',
}) => {
  const orientationClasses = {
    horizontal: 'flex-row',
    vertical: 'flex-col',
  };

  const spacingClasses = {
    none: 'gap-0',
    sm: 'gap-2',
    md: 'gap-3',
    lg: 'gap-4',
  };

  return (
    <div
      className={cn(
        'flex',
        orientationClasses[orientation],
        spacingClasses[spacing],
        className
      )}
    >
      {children}
    </div>
  );
};

// Icon Button Component
interface IconButtonProps extends Omit<ButtonProps, 'leftIcon' | 'rightIcon' | 'children'> {
  icon: React.ReactNode;
  'aria-label': string;
  tooltip?: string;
}

export const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ icon, size = 'md', variant = 'ghost', className, ...props }, ref) => {
    const sizeClasses = {
      sm: 'w-8 h-8 p-1',
      md: 'w-10 h-10 p-2',
      lg: 'w-12 h-12 p-2.5',
      xl: 'w-14 h-14 p-3',
    };

    return (
      <Button
        ref={ref}
        variant={variant}
        size={size}
        className={cn(
          'aspect-square !px-0',
          sizeClasses[size],
          className
        )}
        {...props}
      >
        {icon}
      </Button>
    );
  }
);

IconButton.displayName = 'IconButton';

export default Button;
