import { Router, Request, Response } from 'express';
import { supabase } from '@/config/supabase';
import { redis } from '@/config/redis';
import { config } from '@/config/environment';

const router = Router();

// Health check endpoint
router.get('/', async (req: Request, res: Response) => {
  try {
    const healthCheck = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: config.env,
      version: process.env.npm_package_version || '1.0.0',
      services: {
        database: 'unknown',
        redis: 'unknown',
      },
    };

    // Check database connection (non-blocking)
    try {
      const result = await Promise.race([
        supabase.getClient().from('users').select('count').limit(1),
        new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), 2000))
      ]) as any;
      healthCheck.services.database = result.error ? 'degraded' : 'healthy';
    } catch (error) {
      healthCheck.services.database = 'degraded';
    }

    // Check Redis connection (non-blocking)
    try {
      const isRedisHealthy = await Promise.race([
        redis.healthCheck(),
        new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), 2000))
      ]);
      healthCheck.services.redis = isRedisHealthy ? 'healthy' : 'degraded';
    } catch (error) {
      healthCheck.services.redis = 'degraded';
    }

    // Always return 200 for basic health check - services can be degraded but server is running
    res.status(200).json(healthCheck);
  } catch (error) {
    res.status(200).json({
      status: 'degraded',
      timestamp: new Date().toISOString(),
      error: 'Health check partially failed',
      uptime: process.uptime(),
    });
  }
});

export default router;
