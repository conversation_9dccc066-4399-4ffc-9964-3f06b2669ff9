import { Router, Request, Response } from 'express';
import { supabase } from '@/config/supabase';
import { redis } from '@/config/redis';
import { config } from '@/config/environment';

const router = Router();

// Health check endpoint
router.get('/', async (req: Request, res: Response) => {
  try {
    const healthCheck = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: config.env,
      version: process.env.npm_package_version || '1.0.0',
      services: {
        database: 'unknown',
        redis: 'unknown',
      },
    };

    // Check database connection
    try {
      const { data, error } = await supabase.getClient().from('users').select('count').limit(1);
      healthCheck.services.database = error ? 'unhealthy' : 'healthy';
      if (error) {
        healthCheck.status = 'degraded';
      }
    } catch (error) {
      healthCheck.services.database = 'unhealthy';
      healthCheck.status = 'degraded';
    }

    // Check Redis connection
    try {
      const isRedisHealthy = await redis.healthCheck();
      healthCheck.services.redis = isRedisHealthy ? 'healthy' : 'unhealthy';
      if (!isRedisHealthy) {
        healthCheck.status = 'degraded';
      }
    } catch (error) {
      healthCheck.services.redis = 'unhealthy';
      healthCheck.status = 'degraded';
    }

    const statusCode = healthCheck.status === 'healthy' ? 200 : 503;
    res.status(statusCode).json(healthCheck);
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
    });
  }
});

export default router;
