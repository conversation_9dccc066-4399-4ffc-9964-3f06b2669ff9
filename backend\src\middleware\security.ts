import { Request, Response, NextFunction } from 'express';
import { logger } from '../config/logger';

// Security middleware
export const securityMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Add security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

  // Log security events
  if (req.path.includes('admin') || req.path.includes('auth')) {
    logger.info('Security-sensitive endpoint accessed:', {
      path: req.path,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });
  }

  next();
};

export default securityMiddleware;
