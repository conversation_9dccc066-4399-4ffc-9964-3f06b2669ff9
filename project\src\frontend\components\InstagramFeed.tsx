import React from 'react';
import { Instagram, Heart, MessageCircle, Share } from 'lucide-react';

interface InstagramPost {
  id: string;
  image: string;
  caption: string;
  likes: number;
  comments: number;
}

const InstagramFeed: React.FC = () => {
  // Mock Instagram posts data
  const posts: InstagramPost[] = [
    {
      id: '1',
      image: 'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?auto=compress&cs=tinysrgb&w=400',
      caption: 'Beautiful moments captured ✨',
      likes: 234,
      comments: 12
    },
    {
      id: '2',
      image: 'https://images.pexels.com/photos/1640774/pexels-photo-1640774.jpeg?auto=compress&cs=tinysrgb&w=400',
      caption: 'Living our best life 🌟',
      likes: 189,
      comments: 8
    },
    {
      id: '3',
      image: 'https://images.pexels.com/photos/1640770/pexels-photo-1640770.jpeg?auto=compress&cs=tinysrgb&w=400',
      caption: 'Inspiration everywhere 💫',
      likes: 156,
      comments: 15
    },
    {
      id: '4',
      image: 'https://images.pexels.com/photos/1640771/pexels-photo-1640771.jpeg?auto=compress&cs=tinysrgb&w=400',
      caption: 'Making memories that last 📸',
      likes: 298,
      comments: 23
    }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <Instagram className="h-8 w-8 text-pink-500 mr-3" />
            <h2 className="text-3xl font-bold text-gray-900">Follow Us on Instagram</h2>
          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Stay connected and see what we're up to. Follow us for behind-the-scenes content, 
            product updates, and daily inspiration.
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {posts.map((post) => (
            <div 
              key={post.id} 
              className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 group"
            >
              <div className="relative overflow-hidden">
                <img 
                  src={post.image} 
                  alt={post.caption}
                  className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity duration-300 flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex space-x-4">
                    <div className="flex items-center text-white">
                      <Heart className="h-5 w-5 mr-1" />
                      <span className="font-semibold">{post.likes}</span>
                    </div>
                    <div className="flex items-center text-white">
                      <MessageCircle className="h-5 w-5 mr-1" />
                      <span className="font-semibold">{post.comments}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="p-4">
                <p className="text-gray-800 text-sm leading-relaxed">{post.caption}</p>
                <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-100">
                  <div className="flex items-center space-x-4 text-gray-500">
                    <button className="flex items-center hover:text-red-500 transition-colors">
                      <Heart className="h-4 w-4 mr-1" />
                      <span className="text-sm">{post.likes}</span>
                    </button>
                    <button className="flex items-center hover:text-blue-500 transition-colors">
                      <MessageCircle className="h-4 w-4 mr-1" />
                      <span className="text-sm">{post.comments}</span>
                    </button>
                  </div>
                  <button className="text-gray-500 hover:text-gray-700 transition-colors">
                    <Share className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <a 
            href="https://instagram.com" 
            target="_blank" 
            rel="noopener noreferrer"
            className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-300 transform hover:scale-105 shadow-lg"
          >
            <Instagram className="h-5 w-5 mr-2" />
            Follow Us @ourhandle
          </a>
        </div>
      </div>
    </section>
  );
};

export default InstagramFeed;