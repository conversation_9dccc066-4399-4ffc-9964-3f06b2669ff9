import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  LineChart, Line, PieChart, Pie, Cell, AreaChart, Area
} from 'recharts';
import {
  Users, ShoppingCart, Eye, TrendingUp, Clock, MapPin,
  Smartphone, Monitor, Tablet, Globe
} from 'lucide-react';
import { AnalyticsMetrics, ProductAnalytics } from '../../shared/types';

interface AnalyticsDashboardProps {
  className?: string;
}

const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({ className = '' }) => {
  const [timeRange, setTimeRange] = useState<'24h' | '7d' | '30d' | '90d'>('7d');
  const [metrics, setMetrics] = useState<AnalyticsMetrics | null>(null);
  const [productAnalytics, setProductAnalytics] = useState<ProductAnalytics[]>([]);
  const [loading, setLoading] = useState(true);

  // Mock data - replace with real API calls
  useEffect(() => {
    const fetchAnalytics = async () => {
      setLoading(true);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock metrics data
      setMetrics({
        totalUsers: 12543,
        activeUsers: 3421,
        pageViews: 45678,
        sessions: 8765,
        bounceRate: 0.32,
        averageSessionDuration: 245,
        conversionRate: 0.034,
        revenue: 23456.78,
      });

      // Mock product analytics
      setProductAnalytics([
        {
          productId: '1',
          views: 1234,
          clicks: 456,
          addToCarts: 123,
          purchases: 45,
          revenue: 2345.67,
          conversionRate: 0.036,
          averageTimeSpent: 180,
        },
        {
          productId: '2',
          views: 987,
          clicks: 321,
          addToCarts: 87,
          purchases: 32,
          revenue: 1876.54,
          conversionRate: 0.032,
          averageTimeSpent: 165,
        },
      ]);

      setLoading(false);
    };

    fetchAnalytics();
  }, [timeRange]);

  // Mock data for charts
  const trafficData = [
    { name: 'Mon', users: 1200, sessions: 1800, pageViews: 3200 },
    { name: 'Tue', users: 1100, sessions: 1650, pageViews: 2900 },
    { name: 'Wed', users: 1300, sessions: 1950, pageViews: 3400 },
    { name: 'Thu', users: 1400, sessions: 2100, pageViews: 3600 },
    { name: 'Fri', users: 1600, sessions: 2400, pageViews: 4100 },
    { name: 'Sat', users: 1800, sessions: 2700, pageViews: 4500 },
    { name: 'Sun', users: 1500, sessions: 2250, pageViews: 3800 },
  ];

  const deviceData = [
    { name: 'Mobile', value: 65, color: '#3B82F6' },
    { name: 'Desktop', value: 25, color: '#10B981' },
    { name: 'Tablet', value: 10, color: '#F59E0B' },
  ];

  const topPagesData = [
    { page: '/products', views: 8765, uniqueViews: 6543 },
    { page: '/', views: 7654, uniqueViews: 5432 },
    { page: '/about', views: 3456, uniqueViews: 2876 },
    { page: '/contact', views: 2345, uniqueViews: 1987 },
    { page: '/gifting', views: 1876, uniqueViews: 1543 },
  ];

  const conversionFunnelData = [
    { step: 'Landing', users: 10000, percentage: 100 },
    { step: 'Product View', users: 6500, percentage: 65 },
    { step: 'Add to Cart', users: 2600, percentage: 26 },
    { step: 'Checkout', users: 1300, percentage: 13 },
    { step: 'Purchase', users: 340, percentage: 3.4 },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600 mt-1">Monitor user behavior and business performance</p>
        </div>
        
        {/* Time Range Selector */}
        <div className="flex bg-gray-100 rounded-lg p-1 mt-4 sm:mt-0">
          {(['24h', '7d', '30d', '90d'] as const).map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                timeRange === range
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {range}
            </button>
          ))}
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Users"
          value={metrics?.totalUsers.toLocaleString() || '0'}
          change="+12.5%"
          changeType="positive"
          icon={<Users className="w-5 h-5" />}
        />
        <MetricCard
          title="Active Users"
          value={metrics?.activeUsers.toLocaleString() || '0'}
          change="+8.2%"
          changeType="positive"
          icon={<Globe className="w-5 h-5" />}
        />
        <MetricCard
          title="Conversion Rate"
          value={`${((metrics?.conversionRate || 0) * 100).toFixed(1)}%`}
          change="+2.1%"
          changeType="positive"
          icon={<TrendingUp className="w-5 h-5" />}
        />
        <MetricCard
          title="Revenue"
          value={`$${metrics?.revenue.toLocaleString() || '0'}`}
          change="+15.3%"
          changeType="positive"
          icon={<ShoppingCart className="w-5 h-5" />}
        />
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Traffic Overview */}
        <div className="bg-white p-6 rounded-xl shadow-soft">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Traffic Overview</h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={trafficData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Area
                type="monotone"
                dataKey="pageViews"
                stackId="1"
                stroke="#3B82F6"
                fill="#3B82F6"
                fillOpacity={0.6}
              />
              <Area
                type="monotone"
                dataKey="sessions"
                stackId="1"
                stroke="#10B981"
                fill="#10B981"
                fillOpacity={0.6}
              />
              <Area
                type="monotone"
                dataKey="users"
                stackId="1"
                stroke="#F59E0B"
                fill="#F59E0B"
                fillOpacity={0.6}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Device Breakdown */}
        <div className="bg-white p-6 rounded-xl shadow-soft">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Device Breakdown</h3>
          <div className="flex items-center justify-center">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={deviceData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={120}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {deviceData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
          <div className="flex justify-center space-x-6 mt-4">
            {deviceData.map((item) => (
              <div key={item.name} className="flex items-center">
                <div
                  className="w-3 h-3 rounded-full mr-2"
                  style={{ backgroundColor: item.color }}
                />
                <span className="text-sm text-gray-600">
                  {item.name} ({item.value}%)
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Conversion Funnel */}
      <div className="bg-white p-6 rounded-xl shadow-soft">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Conversion Funnel</h3>
        <div className="space-y-4">
          {conversionFunnelData.map((step, index) => (
            <div key={step.step} className="flex items-center">
              <div className="w-32 text-sm font-medium text-gray-700">
                {step.step}
              </div>
              <div className="flex-1 mx-4">
                <div className="bg-gray-200 rounded-full h-6 relative overflow-hidden">
                  <motion.div
                    className="bg-blue-500 h-full rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${step.percentage}%` }}
                    transition={{ duration: 1, delay: index * 0.1 }}
                  />
                  <div className="absolute inset-0 flex items-center justify-center text-xs font-medium text-white">
                    {step.percentage}%
                  </div>
                </div>
              </div>
              <div className="w-20 text-right text-sm text-gray-600">
                {step.users.toLocaleString()}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Top Pages */}
      <div className="bg-white p-6 rounded-xl shadow-soft">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Pages</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-2 text-sm font-medium text-gray-600">Page</th>
                <th className="text-right py-2 text-sm font-medium text-gray-600">Views</th>
                <th className="text-right py-2 text-sm font-medium text-gray-600">Unique Views</th>
              </tr>
            </thead>
            <tbody>
              {topPagesData.map((page) => (
                <tr key={page.page} className="border-b border-gray-100">
                  <td className="py-3 text-sm text-gray-900">{page.page}</td>
                  <td className="py-3 text-sm text-gray-600 text-right">
                    {page.views.toLocaleString()}
                  </td>
                  <td className="py-3 text-sm text-gray-600 text-right">
                    {page.uniqueViews.toLocaleString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

// Metric Card Component
interface MetricCardProps {
  title: string;
  value: string;
  change: string;
  changeType: 'positive' | 'negative' | 'neutral';
  icon: React.ReactNode;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  changeType,
  icon,
}) => {
  const changeColor = {
    positive: 'text-green-600',
    negative: 'text-red-600',
    neutral: 'text-gray-600',
  }[changeType];

  return (
    <motion.div
      className="bg-white p-6 rounded-xl shadow-soft"
      whileHover={{ y: -2 }}
      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          <p className={`text-sm mt-1 ${changeColor}`}>{change}</p>
        </div>
        <div className="text-blue-600">{icon}</div>
      </div>
    </motion.div>
  );
};

export default AnalyticsDashboard;
