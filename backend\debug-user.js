const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugUser() {
  console.log('🔍 Debugging user data...\n');
  
  try {
    // Get all users
    const { data: users, error } = await supabase
      .from('users')
      .select('*');
    
    if (error) {
      console.error('❌ Error fetching users:', error);
      return;
    }
    
    console.log(`📊 Found ${users.length} users:`);
    users.forEach((user, index) => {
      console.log(`\n👤 User ${index + 1}:`);
      console.log(`   ID: ${user.id}`);
      console.log(`   Email: ${user.email}`);
      console.log(`   Name: ${user.name}`);
      console.log(`   Role: ${user.role}`);
      console.log(`   Password: ${user.password ? 'EXISTS (length: ' + user.password.length + ')' : 'MISSING'}`);
      console.log(`   Created: ${user.created_at}`);
      console.log(`   Login Attempts: ${user.login_attempts || 0}`);
      console.log(`   Locked Until: ${user.locked_until || 'Not locked'}`);
    });
    
    // Test specific user lookup
    if (users.length > 0) {
      const testEmail = users[0].email;
      console.log(`\n🔍 Testing lookup for email: ${testEmail}`);
      
      const { data: user, error: lookupError } = await supabase
        .from('users')
        .select('*')
        .eq('email', testEmail)
        .single();
      
      if (lookupError) {
        console.error('❌ Lookup error:', lookupError);
      } else {
        console.log('✅ Lookup successful:');
        console.log(`   Password field: ${user.password ? 'EXISTS' : 'MISSING'}`);
        console.log(`   All fields:`, Object.keys(user));
      }
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

debugUser();
