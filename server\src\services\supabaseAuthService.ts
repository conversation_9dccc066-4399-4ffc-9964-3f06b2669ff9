import { supabase } from '@/config/supabase';
import { redis } from '@/config/redis';
import { config } from '@/config/environment';
import { logger } from '@/config/logger';

// Supabase Authentication Service for WhiskAffair
// Professional authentication with Supabase Auth + custom user management

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
}

export interface AuthResult {
  user: any;
  session: any;
  requiresTwoFactor?: boolean;
  twoFactorToken?: string;
}

export class SupabaseAuthService {
  // Register new user
  async register(data: RegisterData, ipAddress?: string, userAgent?: string): Promise<AuthResult> {
    try {
      // Check rate limiting
      await this.checkRateLimit(`register:${ipAddress}`, 5, 15 * 60 * 1000);

      // Create user with Supabase Auth
      const { data: authData, error: authError } = await supabase
        .getClient()
        .auth.signUp({
          email: data.email,
          password: data.password,
          options: {
            data: {
              first_name: data.firstName,
              last_name: data.lastName,
              phone: data.phone,
            },
          },
        });

      if (authError) throw authError;

      if (!authData.user) {
        throw new Error('User creation failed');
      }

      // Create user profile in our users table
      const { error: profileError } = await supabase
        .getAdminClient()
        .from('users')
        .insert({
          id: authData.user.id,
          email: data.email,
          first_name: data.firstName,
          last_name: data.lastName,
          phone: data.phone,
          role: 'CUSTOMER',
          email_verified: authData.user.email_confirmed_at ? true : false,
        });

      if (profileError) {
        logger.error('Failed to create user profile:', profileError);
        // Don't throw here as the auth user was created successfully
      }

      // Log security event
      await supabase.logSecurityEvent(
        'user_registered',
        { email: data.email, method: 'email_password' },
        authData.user.id,
        ipAddress,
        userAgent
      );

      return {
        user: authData.user,
        session: authData.session,
      };
    } catch (error) {
      logger.error('Registration failed:', error);
      throw error;
    }
  }

  // Login user
  async login(
    credentials: LoginCredentials,
    ipAddress?: string,
    userAgent?: string
  ): Promise<AuthResult> {
    try {
      const { email, password } = credentials;

      // Check rate limiting
      await this.checkRateLimit(`login:${email}`, 5, 15 * 60 * 1000);

      // Sign in with Supabase Auth
      const { data: authData, error: authError } = await supabase
        .getClient()
        .auth.signInWithPassword({
          email,
          password,
        });

      if (authError) {
        // Log failed login
        await supabase.logSecurityEvent(
          'login_failed',
          { email, reason: authError.message },
          undefined,
          ipAddress,
          userAgent,
          false
        );
        throw authError;
      }

      if (!authData.user || !authData.session) {
        throw new Error('Login failed');
      }

      // Get user profile
      const userProfile = await supabase.getUserById(authData.user.id);

      // Check if account is active
      if (userProfile?.status !== 'ACTIVE') {
        throw new Error('Account is not active');
      }

      // Check if 2FA is enabled
      if (userProfile?.two_factor_enabled) {
        // Generate temporary 2FA token
        const twoFactorToken = crypto.randomUUID();
        await redis.set(`2fa:${twoFactorToken}`, authData.user.id, 300); // 5 minutes

        await supabase.logSecurityEvent(
          'login_2fa_required',
          { email },
          authData.user.id,
          ipAddress,
          userAgent
        );

        return {
          user: authData.user,
          session: null,
          requiresTwoFactor: true,
          twoFactorToken,
        };
      }

      // Update last login
      await supabase.updateUser(authData.user.id, {
        last_login_at: new Date().toISOString(),
        last_login_ip: ipAddress,
        login_attempts: 0,
      });

      // Log successful login
      await supabase.logSecurityEvent(
        'login_success',
        { email },
        authData.user.id,
        ipAddress,
        userAgent
      );

      return {
        user: { ...authData.user, ...userProfile },
        session: authData.session,
      };
    } catch (error) {
      logger.error('Login failed:', error);
      throw error;
    }
  }

  // Logout user
  async logout(userId?: string): Promise<void> {
    try {
      const { error } = await supabase.getClient().auth.signOut();
      
      if (error) {
        logger.error('Logout error:', error);
      }

      // Log logout
      if (userId) {
        await supabase.logSecurityEvent('logout', {}, userId);
      }
    } catch (error) {
      logger.error('Logout failed:', error);
      throw error;
    }
  }

  // Refresh session
  async refreshSession(): Promise<{ session: any; user: any }> {
    try {
      const { data, error } = await supabase.getClient().auth.refreshSession();
      
      if (error) throw error;
      
      return {
        session: data.session,
        user: data.user,
      };
    } catch (error) {
      logger.error('Session refresh failed:', error);
      throw error;
    }
  }

  // Reset password
  async resetPassword(email: string): Promise<void> {
    try {
      const { error } = await supabase.getClient().auth.resetPasswordForEmail(email, {
        redirectTo: `${config.cors.origin}/reset-password`,
      });

      if (error) throw error;

      await supabase.logSecurityEvent(
        'password_reset_requested',
        { email },
        undefined
      );
    } catch (error) {
      logger.error('Password reset failed:', error);
      throw error;
    }
  }

  // Update password
  async updatePassword(newPassword: string): Promise<void> {
    try {
      const { error } = await supabase.getClient().auth.updateUser({
        password: newPassword,
      });

      if (error) throw error;

      const { data: { user } } = await supabase.getClient().auth.getUser();
      
      if (user) {
        await supabase.logSecurityEvent(
          'password_updated',
          {},
          user.id
        );
      }
    } catch (error) {
      logger.error('Password update failed:', error);
      throw error;
    }
  }

  // Get current user
  async getCurrentUser(): Promise<any> {
    try {
      const { data: { user }, error } = await supabase.getClient().auth.getUser();
      
      if (error) throw error;
      
      if (!user) return null;

      // Get full user profile
      const userProfile = await supabase.getUserById(user.id);
      
      return { ...user, ...userProfile };
    } catch (error) {
      logger.error('Get current user failed:', error);
      throw error;
    }
  }

  // Verify email
  async verifyEmail(token: string): Promise<void> {
    try {
      const { error } = await supabase.getClient().auth.verifyOtp({
        token_hash: token,
        type: 'email',
      });

      if (error) throw error;

      const { data: { user } } = await supabase.getClient().auth.getUser();
      
      if (user) {
        await supabase.updateUser(user.id, {
          email_verified: true,
          email_verified_at: new Date().toISOString(),
        });

        await supabase.logSecurityEvent(
          'email_verified',
          {},
          user.id
        );
      }
    } catch (error) {
      logger.error('Email verification failed:', error);
      throw error;
    }
  }

  // Check rate limiting
  private async checkRateLimit(
    identifier: string,
    maxAttempts: number,
    windowMs: number
  ): Promise<void> {
    const key = `rate_limit:${identifier}`;
    const attempts = await redis.incrementWithExpiry(key, Math.ceil(windowMs / 1000));

    if (attempts > maxAttempts) {
      const ttl = await redis.getClient().ttl(key);
      throw new Error(
        `Too many attempts. Try again in ${Math.ceil(ttl / 60)} minutes.`
      );
    }
  }

  // Setup 2FA (placeholder - implement with your preferred 2FA library)
  async setup2FA(userId: string): Promise<{ secret: string; qrCodeUrl: string }> {
    // Implementation depends on your 2FA library choice
    // This is a placeholder
    throw new Error('2FA setup not implemented yet');
  }

  // Enable 2FA
  async enable2FA(userId: string, code: string): Promise<void> {
    // Implementation depends on your 2FA library choice
    // This is a placeholder
    throw new Error('2FA enable not implemented yet');
  }

  // Disable 2FA
  async disable2FA(userId: string, code: string): Promise<void> {
    // Implementation depends on your 2FA library choice
    // This is a placeholder
    throw new Error('2FA disable not implemented yet');
  }
}

export const supabaseAuthService = new SupabaseAuthService();
