import React from 'react';
import { motion } from 'framer-motion';
import { MapPin, Phone, Mail, Instagram, Facebook, Twitter, Heart, Clock, Award } from 'lucide-react';

const Footer: React.FC = () => {
  const footerLinks = {
    company: [
      { name: 'About Us', href: '#' },
      { name: 'Our Story', href: '#story' },
      { name: 'Careers', href: '#' },
      { name: 'Press', href: '#' },
    ],
    products: [
      { name: 'Cookies', href: '#' },
      { name: 'Cakes', href: '#' },
      { name: 'Pastries', href: '#' },
      { name: 'Seasonal', href: '#' },
    ],
    services: [
      { name: 'Custom Orders', href: '#' },
      { name: 'Corporate Gifting', href: '#' },
      { name: 'Wedding Cakes', href: '#' },
      { name: 'Catering', href: '#' },
    ],
    support: [
      { name: 'Contact Us', href: '#' },
      { name: 'FAQs', href: '#' },
      { name: 'Shipping Info', href: '#' },
      { name: 'Returns', href: '#' },
    ],
  };

  const socialLinks = [
    { icon: Instagram, href: '#', color: 'hover:text-pink-500' },
    { icon: Facebook, href: '#', color: 'hover:text-blue-500' },
    { icon: Twitter, href: '#', color: 'hover:text-blue-400' },
  ];

  const trustBadges = [
    { icon: Heart, text: '100% Butter-Free' },
    { icon: Award, text: 'Premium Quality' },
    { icon: Clock, text: 'Fresh Daily' },
  ];

  return (
    <footer className="bg-taupe text-cream">
      {/* Newsletter Section */}
      <div className="border-b border-cream/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <h3 className="font-playfair text-3xl font-bold mb-4">
              Join Our Sweet Community
            </h3>
            <p className="font-montserrat text-cream/80 mb-8 max-w-2xl mx-auto">
              Be the first to know about new flavors, exclusive offers, and seasonal collections. 
              Plus, get 10% off your first order!
            </p>
            <div className="flex flex-col sm:flex-row max-w-md mx-auto gap-4">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-6 py-3 rounded-full bg-cream/10 border border-cream/20 text-cream placeholder-cream/60 focus:outline-none focus:border-gold"
              />
              <button className="px-8 py-3 bg-gradient-gold text-taupe rounded-full font-montserrat font-semibold hover:shadow-gold transition-all duration-300">
                Subscribe
              </button>
            </div>
            <p className="font-montserrat text-xs text-cream/60 mt-4">
              No spam, just sweet updates. Unsubscribe anytime.
            </p>
          </motion.div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-12">
          {/* Brand Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="lg:col-span-2"
          >
            <h2 className="font-playfair text-3xl font-bold mb-4">
              Whisk Affair
              <span className="block text-lg font-montserrat font-light text-gold tracking-widest">
                BY JINALI
              </span>
            </h2>
            <p className="font-montserrat text-cream/80 mb-6 leading-relaxed">
              Mumbai's premier destination for luxury butter-free desserts. 
              Every creation is crafted with love, premium ingredients, and an 
              unwavering commitment to excellence.
            </p>
            
            {/* Trust Badges */}
            <div className="flex flex-wrap gap-4 mb-8">
              {trustBadges.map((badge, index) => (
                <div key={index} className="flex items-center space-x-2 bg-cream/10 px-4 py-2 rounded-full">
                  <badge.icon className="w-4 h-4 text-gold" />
                  <span className="font-montserrat text-sm">{badge.text}</span>
                </div>
              ))}
            </div>

            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <MapPin className="w-5 h-5 text-gold mt-0.5 flex-shrink-0" />
                <div className="font-montserrat text-sm text-cream/80">
                  <p>123 Bandra West, Mumbai</p>
                  <p>Maharashtra 400050, India</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="w-5 h-5 text-gold flex-shrink-0" />
                <a href="tel:+************" className="font-montserrat text-sm text-cream/80 hover:text-gold transition-colors">
                  +91 98765 43210
                </a>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-gold flex-shrink-0" />
                <a href="mailto:<EMAIL>" className="font-montserrat text-sm text-cream/80 hover:text-gold transition-colors">
                  <EMAIL>
                </a>
              </div>
            </div>
          </motion.div>

          {/* Links Sections */}
          {Object.entries(footerLinks).map(([category, links], index) => (
            <motion.div
              key={category}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <h4 className="font-montserrat font-semibold text-gold mb-4 capitalize">
                {category}
              </h4>
              <ul className="space-y-3">
                {links.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="font-montserrat text-sm text-cream/80 hover:text-gold transition-colors duration-300"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-cream/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="font-montserrat text-sm text-cream/60 mb-4 md:mb-0">
              <p>&copy; 2024 Whisk Affair by Jinali. All rights reserved.</p>
              <p className="mt-1">
                <a href="#" className="hover:text-gold transition-colors">Privacy Policy</a> • 
                <a href="#" className="hover:text-gold transition-colors ml-1">Terms of Service</a> • 
                <a href="#" className="hover:text-gold transition-colors ml-1">Cookie Policy</a>
              </p>
            </div>
            
            {/* Social Links */}
            <div className="flex items-center space-x-4">
              <span className="font-montserrat text-sm text-cream/60">Follow us:</span>
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  className={`p-2 bg-cream/10 rounded-full transition-all duration-300 ${social.color}`}
                >
                  <social.icon className="w-4 h-4" />
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;