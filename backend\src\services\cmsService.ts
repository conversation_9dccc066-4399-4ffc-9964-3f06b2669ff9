import { supabase } from '@/config/supabase';
import { ContentPage, Inquiry, Inserts, Updates } from '@/types/supabase';

export class CMSService {
  // Content Pages CRUD operations
  async getAllPages(filters?: {
    is_published?: boolean;
    search?: string;
    limit?: number;
    offset?: number;
  }) {
    let query = supabase.getAdminClient()
      .from('content_pages')
      .select('*');

    if (filters?.is_published !== undefined) {
      query = query.eq('is_published', filters.is_published);
    }

    if (filters?.search) {
      query = query.or(`title.ilike.%${filters.search}%,content.ilike.%${filters.search}%`);
    }

    if (filters?.limit) {
      query = query.limit(filters.limit);
    }

    if (filters?.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
    }

    query = query.order('created_at', { ascending: false });

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch pages: ${error.message}`);
    }

    return data;
  }

  async getPageById(id: string) {
    const { data, error } = await supabase.getAdminClient()
      .from('content_pages')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      throw new Error(`Failed to fetch page: ${error.message}`);
    }

    return data;
  }

  async getPageBySlug(slug: string) {
    const { data, error } = await supabase.getAdminClient()
      .from('content_pages')
      .select('*')
      .eq('slug', slug)
      .eq('is_published', true)
      .single();

    if (error) {
      throw new Error(`Failed to fetch page: ${error.message}`);
    }

    return data;
  }

  async createPage(pageData: Inserts<'content_pages'>) {
    const { data, error } = await supabase.getAdminClient()
      .from('content_pages')
      .insert(pageData)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create page: ${error.message}`);
    }

    return data;
  }

  async updatePage(id: string, pageData: Updates<'content_pages'>) {
    const { data, error } = await supabase.getAdminClient()
      .from('content_pages')
      .update({ ...pageData, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update page: ${error.message}`);
    }

    return data;
  }

  async deletePage(id: string) {
    const { error } = await supabase.getAdminClient()
      .from('content_pages')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to delete page: ${error.message}`);
    }

    return true;
  }

  // Inquiry management
  async getAllInquiries(filters?: {
    status?: 'new' | 'contacted' | 'closed';
    product_id?: string;
    search?: string;
    limit?: number;
    offset?: number;
  }) {
    let query = supabase.getAdminClient()
      .from('inquiries')
      .select(`
        *,
        products (
          id,
          name,
          slug
        )
      `);

    if (filters?.status) {
      query = query.eq('status', filters.status);
    }

    if (filters?.product_id) {
      query = query.eq('product_id', filters.product_id);
    }

    if (filters?.search) {
      query = query.or(`customer_name.ilike.%${filters.search}%,customer_email.ilike.%${filters.search}%,message.ilike.%${filters.search}%`);
    }

    if (filters?.limit) {
      query = query.limit(filters.limit);
    }

    if (filters?.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
    }

    query = query.order('created_at', { ascending: false });

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch inquiries: ${error.message}`);
    }

    return data;
  }

  async getInquiryById(id: string) {
    const { data, error } = await supabase.getAdminClient()
      .from('inquiries')
      .select(`
        *,
        products (
          id,
          name,
          slug
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      throw new Error(`Failed to fetch inquiry: ${error.message}`);
    }

    return data;
  }

  async createInquiry(inquiryData: Inserts<'inquiries'>) {
    const { data, error } = await supabase.getAdminClient()
      .from('inquiries')
      .insert(inquiryData)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create inquiry: ${error.message}`);
    }

    return data;
  }

  async updateInquiryStatus(id: string, status: 'new' | 'contacted' | 'closed') {
    const { data, error } = await supabase.getAdminClient()
      .from('inquiries')
      .update({ 
        status, 
        updated_at: new Date().toISOString() 
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update inquiry status: ${error.message}`);
    }

    return data;
  }

  async deleteInquiry(id: string) {
    const { error } = await supabase.getAdminClient()
      .from('inquiries')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to delete inquiry: ${error.message}`);
    }

    return true;
  }

  // Dashboard statistics
  async getDashboardStats() {
    try {
      // Get total counts
      const [
        { count: totalProducts },
        { count: totalCategories },
        { count: totalInquiries },
        { count: newInquiries },
        { count: publishedPages }
      ] = await Promise.all([
        supabase.getAdminClient().from('products').select('*', { count: 'exact', head: true }),
        supabase.getAdminClient().from('categories').select('*', { count: 'exact', head: true }),
        supabase.getAdminClient().from('inquiries').select('*', { count: 'exact', head: true }),
        supabase.getAdminClient().from('inquiries').select('*', { count: 'exact', head: true }).eq('status', 'new'),
        supabase.getAdminClient().from('content_pages').select('*', { count: 'exact', head: true }).eq('is_published', true)
      ]);

      // Get recent inquiries
      const { data: recentInquiries } = await supabase
        .getAdminClient().from('inquiries')
        .select(`
          *,
          products (
            id,
            name,
            slug
          )
        `)
        .order('created_at', { ascending: false })
        .limit(5);

      // Get popular products (based on inquiry count)
      const { data: popularProducts } = await supabase
        .getAdminClient().from('products')
        .select(`
          *,
          categories (
            id,
            name,
            slug
          )
        `)
        .eq('is_active', true)
        .limit(5)
        .order('created_at', { ascending: false });

      return {
        totalProducts: totalProducts || 0,
        totalCategories: totalCategories || 0,
        totalInquiries: totalInquiries || 0,
        newInquiries: newInquiries || 0,
        publishedPages: publishedPages || 0,
        recentInquiries: recentInquiries || [],
        popularProducts: popularProducts || []
      };
    } catch (error) {
      throw new Error(`Failed to fetch dashboard stats: ${error}`);
    }
  }

  // SEO helpers
  async generateSlug(title: string, table: 'products' | 'categories' | 'content_pages'): Promise<string> {
    const baseSlug = title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();

    let slug = baseSlug;
    let counter = 1;

    while (true) {
      const { data } = await supabase
        .getAdminClient().from(table)
        .select('id')
        .eq('slug', slug)
        .limit(1);

      if (!data || data.length === 0) {
        break;
      }

      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    return slug;
  }
}

export const cmsService = new CMSService();
