import { Server as SocketIOServer, Socket } from 'socket.io';
import { logger } from '@/config/logger';

export const setupSocketHandlers = (io: SocketIOServer): void => {
  io.on('connection', (socket: Socket) => {
    logger.info(`Socket connected: ${socket.id}`);

    // Handle real-time analytics updates
    socket.on('join-analytics', () => {
      socket.join('analytics');
      logger.info(`Socket ${socket.id} joined analytics room`);
    });

    socket.on('leave-analytics', () => {
      socket.leave('analytics');
      logger.info(`Socket ${socket.id} left analytics room`);
    });

    socket.on('disconnect', () => {
      logger.info(`Socket disconnected: ${socket.id}`);
    });
  });
};

export default setupSocketHandlers;
