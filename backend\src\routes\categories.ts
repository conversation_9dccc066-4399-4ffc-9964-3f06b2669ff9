import express, { Request, Response } from 'express';
import { body, query, validationResult } from 'express-validator';
import { productService } from '@/services/productService';
import { cmsService } from '@/services/cmsService';
import { authenticateToken } from '@/middleware/auth';

const router = express.Router();

// Public routes - Frontend API
// Get all active categories
router.get('/', [
  query('limit').optional().isInt({ min: 1, max: 100 })
], async (req: Request, res: Response): Promise<any> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const limit = parseInt(req.query.limit as string) || 50;

    const categories = await productService.getAllCategories({
      is_active: true,
      limit
    });

    res.json(categories);
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({ error: 'Failed to fetch categories' });
  }
});

// Get category by ID (public)
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const category = await productService.getCategoryById(req.params.id);
    res.json(category);
  } catch (error) {
    console.error('Error fetching category:', error);
    res.status(404).json({ error: 'Category not found' });
  }
});

// Admin routes - require authentication
// Get all categories for admin (including inactive)
router.get('/admin/all', authenticateToken, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('is_active').optional().isBoolean()
], async (req: Request, res: Response): Promise<any> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const offset = (page - 1) * limit;

    const filters = {
      is_active: req.query.is_active === 'true' ? true : req.query.is_active === 'false' ? false : undefined,
      limit,
      offset
    };

    const categories = await productService.getAllCategories(filters);

    res.json({
      categories,
      pagination: {
        page,
        limit,
        hasMore: categories.length === limit
      }
    });
  } catch (error) {
    console.error('Error fetching admin categories:', error);
    res.status(500).json({ error: 'Failed to fetch categories' });
  }
});

// Create new category (admin only)
router.post('/', authenticateToken, [
  body('name').notEmpty().withMessage('Name is required'),
  body('slug').notEmpty().withMessage('Slug is required'),
  body('description').optional().isString(),
  body('image_url').optional().isURL(),
  body('sort_order').optional().isInt({ min: 0 })
], async (req: Request, res: Response): Promise<any> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const categoryData = {
      ...req.body,
      is_active: req.body.is_active ?? true,
      sort_order: req.body.sort_order ?? 0
    };

    const category = await productService.createCategory(categoryData);
    res.status(201).json(category);
  } catch (error) {
    console.error('Error creating category:', error);
    res.status(500).json({ error: 'Failed to create category' });
  }
});

// Update category (admin only)
router.put('/:id', authenticateToken, [
  body('name').optional().notEmpty(),
  body('slug').optional().notEmpty(),
  body('description').optional().isString(),
  body('image_url').optional().isURL(),
  body('sort_order').optional().isInt({ min: 0 }),
  body('is_active').optional().isBoolean()
], async (req: Request, res: Response): Promise<any> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const category = await productService.updateCategory(req.params.id, req.body);
    res.json(category);
  } catch (error) {
    console.error('Error updating category:', error);
    res.status(500).json({ error: 'Failed to update category' });
  }
});

// Delete category (admin only)
router.delete('/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    await productService.deleteCategory(req.params.id);
    res.json({ message: 'Category deleted successfully' });
  } catch (error) {
    console.error('Error deleting category:', error);
    if (error.message.includes('Cannot delete category with existing products')) {
      res.status(400).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'Failed to delete category' });
    }
  }
});

// Generate slug for category (admin only)
router.post('/generate-slug', authenticateToken, [
  body('title').notEmpty().withMessage('Title is required')
], async (req: Request, res: Response): Promise<any> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const slug = await cmsService.generateSlug(req.body.title, 'categories');
    res.json({ slug });
  } catch (error) {
    console.error('Error generating slug:', error);
    res.status(500).json({ error: 'Failed to generate slug' });
  }
});

export default router;
