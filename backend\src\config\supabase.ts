import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { logger } from './logger';
import { config } from './environment';

// Supabase Database Configuration for WhiskAffair
// Professional setup with connection pooling and error handling

interface SupabaseConfig {
  url: string;
  anonKey: string;
  serviceRoleKey: string;
}

class SupabaseManager {
  private client: SupabaseClient;
  private adminClient: SupabaseClient;
  private isConnected: boolean = false;

  constructor() {
    // Initialize Supabase clients
    this.client = createClient(
      config.supabase.url,
      config.supabase.anonKey,
      {
        auth: {
          autoRefreshToken: true,
          persistSession: true,
          detectSessionInUrl: false,
        },
        realtime: {
          params: {
            eventsPerSecond: 10,
          },
        },
        global: {
          headers: {
            'X-Client-Info': 'whisk-affair-backend',
          },
        },
      }
    );

    // Admin client with service role key for server-side operations
    this.adminClient = createClient(
      config.supabase.url,
      config.supabase.serviceRoleKey,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
        global: {
          headers: {
            'X-Client-Info': 'whisk-affair-admin',
          },
        },
      }
    );

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    // Handle auth state changes
    this.client.auth.onAuthStateChange((event, session) => {
      logger.info(`Supabase auth event: ${event}`, {
        userId: session?.user?.id,
        email: session?.user?.email,
      });
    });
  }

  async connect(): Promise<void> {
    try {
      // Test connection with a simple query
      const { data, error } = await this.adminClient
        .from('users')
        .select('count')
        .limit(1);

      if (error && error.code !== 'PGRST116') { // PGRST116 = table doesn't exist yet
        throw error;
      }

      this.isConnected = true;
      logger.info('✅ Supabase connected successfully');
    } catch (error) {
      logger.error('❌ Failed to connect to Supabase:', error);
      throw error;
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      const { data, error } = await this.adminClient
        .from('users')
        .select('count')
        .limit(1);

      return !error || error.code === 'PGRST116';
    } catch (error) {
      logger.error('Supabase health check failed:', error);
      return false;
    }
  }

  // Get client for user operations (with RLS)
  getClient(): SupabaseClient {
    return this.client;
  }

  // Get admin client for server operations (bypasses RLS)
  getAdminClient(): SupabaseClient {
    return this.adminClient;
  }

  // User management methods
  async createUser(email: string, password: string, userData: any) {
    try {
      const { data, error } = await this.adminClient.auth.admin.createUser({
        email,
        password,
        user_metadata: userData,
        email_confirm: true,
      });

      if (error) throw error;

      // Insert additional user data
      const { error: insertError } = await this.adminClient
        .from('users')
        .insert({
          id: data.user.id,
          email: data.user.email,
          first_name: userData.firstName,
          last_name: userData.lastName,
          phone: userData.phone,
          role: userData.role || 'CUSTOMER',
          created_at: new Date().toISOString(),
        });

      if (insertError) throw insertError;

      return data;
    } catch (error) {
      logger.error('Failed to create user:', error);
      throw error;
    }
  }

  async getUserById(userId: string) {
    try {
      const { data, error } = await this.adminClient
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      logger.error('Failed to get user:', error);
      throw error;
    }
  }

  async updateUser(userId: string, updates: any) {
    try {
      const { data, error } = await this.adminClient
        .from('users')
        .update(updates)
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      logger.error('Failed to update user:', error);
      throw error;
    }
  }

  // Analytics methods
  async trackEvent(event: any) {
    try {
      const { data, error } = await this.adminClient
        .from('analytics_events')
        .insert(event)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      logger.error('Failed to track event:', error);
      throw error;
    }
  }

  async getAnalyticsData(startDate: string, endDate: string, filters: any = {}) {
    try {
      let query = this.adminClient
        .from('analytics_events')
        .select('*')
        .gte('created_at', startDate)
        .lte('created_at', endDate);

      // Apply filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          query = query.eq(key, value);
        }
      });

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    } catch (error) {
      logger.error('Failed to get analytics data:', error);
      throw error;
    }
  }

  // Security logging
  async logSecurityEvent(event: string, details: any, userId?: string, ipAddress?: string, userAgent?: string) {
    try {
      const { data, error } = await this.adminClient
        .from('security_logs')
        .insert({
          event,
          details,
          user_id: userId,
          ip_address: ipAddress,
          user_agent: userAgent,
          success: details.success !== false,
          created_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      logger.error('Failed to log security event:', error);
      throw error;
    }
  }

  // Real-time subscriptions
  subscribeToTable(table: string, callback: (payload: any) => void, filter?: string) {
    let subscription = this.client
      .channel(`${table}-changes`)
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table,
          filter 
        }, 
        callback
      )
      .subscribe();

    return subscription;
  }

  // Batch operations
  async batchInsert(table: string, records: any[]) {
    try {
      const { data, error } = await this.adminClient
        .from(table)
        .insert(records)
        .select();

      if (error) throw error;
      return data;
    } catch (error) {
      logger.error(`Failed to batch insert into ${table}:`, error);
      throw error;
    }
  }

  async batchUpdate(table: string, updates: Array<{ id: string; data: any }>) {
    try {
      const promises = updates.map(({ id, data }) =>
        this.adminClient
          .from(table)
          .update(data)
          .eq('id', id)
          .select()
          .single()
      );

      const results = await Promise.all(promises);
      return results.map(result => result.data);
    } catch (error) {
      logger.error(`Failed to batch update ${table}:`, error);
      throw error;
    }
  }

  // File storage operations
  async uploadFile(bucket: string, path: string, file: File | Buffer, options?: any) {
    try {
      const { data, error } = await this.adminClient.storage
        .from(bucket)
        .upload(path, file, options);

      if (error) throw error;
      return data;
    } catch (error) {
      logger.error('Failed to upload file:', error);
      throw error;
    }
  }

  async getFileUrl(bucket: string, path: string) {
    try {
      const { data } = this.adminClient.storage
        .from(bucket)
        .getPublicUrl(path);

      return data.publicUrl;
    } catch (error) {
      logger.error('Failed to get file URL:', error);
      throw error;
    }
  }

  // Cleanup and disconnect
  async disconnect(): Promise<void> {
    try {
      // Remove all subscriptions
      this.client.removeAllChannels();
      this.isConnected = false;
      logger.info('Supabase disconnected successfully');
    } catch (error) {
      logger.error('Error disconnecting from Supabase:', error);
    }
  }

  isReady(): boolean {
    return this.isConnected;
  }
}

// Create global Supabase instance
export const supabase = new SupabaseManager();

// Connection management
export const connectSupabase = async (): Promise<void> => {
  await supabase.connect();
};

export const disconnectSupabase = async (): Promise<void> => {
  await supabase.disconnect();
};

// Graceful shutdown
process.on('beforeExit', async () => {
  await disconnectSupabase();
});

export default supabase;
