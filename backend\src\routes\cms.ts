import express, { Request, Response } from 'express';
import { body, query, validationResult } from 'express-validator';
import { cmsService } from '@/services/cmsService';
import { authenticateToken } from '@/middleware/auth';

const router = express.Router();

// Public routes - Frontend API
// Get published page by slug
router.get('/pages/:slug', async (req: Request, res: Response) => {
  try {
    const page = await cmsService.getPageBySlug(req.params.slug);
    res.json(page);
  } catch (error) {
    console.error('Error fetching page by slug:', error);
    res.status(404).json({ error: 'Page not found' });
  }
});

// Submit inquiry
router.post('/inquiries', [
  body('customer_name').notEmpty().withMessage('Name is required'),
  body('customer_email').isEmail().withMessage('Valid email is required'),
  body('customer_phone').optional().isMobilePhone('any'),
  body('message').notEmpty().withMessage('Message is required'),
  body('product_id').optional().isUUID()
], async (req: Request, res: Response): Promise<any> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const inquiryData = {
      ...req.body,
      status: 'new' as const
    };

    const inquiry = await cmsService.createInquiry(inquiryData);
    res.status(201).json({ 
      message: 'Inquiry submitted successfully',
      inquiry_id: inquiry.id 
    });
  } catch (error) {
    console.error('Error creating inquiry:', error);
    res.status(500).json({ error: 'Failed to submit inquiry' });
  }
});

// Admin routes - require authentication
// Dashboard statistics
router.get('/admin/dashboard', authenticateToken, async (req: Request, res: Response) => {
  try {
    const stats = await cmsService.getDashboardStats();
    res.json(stats);
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard statistics' });
  }
});

// Content Pages Management
// Get all pages for admin
router.get('/admin/pages', authenticateToken, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('is_published').optional().isBoolean(),
  query('search').optional().isString()
], async (req: Request, res: Response): Promise<any> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const offset = (page - 1) * limit;

    const filters = {
      is_published: req.query.is_published === 'true' ? true : req.query.is_published === 'false' ? false : undefined,
      search: req.query.search as string,
      limit,
      offset
    };

    const pages = await cmsService.getAllPages(filters);

    res.json({
      pages,
      pagination: {
        page,
        limit,
        hasMore: pages.length === limit
      }
    });
  } catch (error) {
    console.error('Error fetching admin pages:', error);
    res.status(500).json({ error: 'Failed to fetch pages' });
  }
});

// Get page by ID for admin
router.get('/admin/pages/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    const page = await cmsService.getPageById(req.params.id);
    res.json(page);
  } catch (error) {
    console.error('Error fetching page:', error);
    res.status(404).json({ error: 'Page not found' });
  }
});

// Create new page (admin only)
router.post('/admin/pages', authenticateToken, [
  body('title').notEmpty().withMessage('Title is required'),
  body('slug').notEmpty().withMessage('Slug is required'),
  body('content').notEmpty().withMessage('Content is required'),
  body('meta_title').optional().isString(),
  body('meta_description').optional().isString(),
  body('is_published').optional().isBoolean()
], async (req: Request, res: Response): Promise<any> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const pageData = {
      ...req.body,
      is_published: req.body.is_published ?? false
    };

    const page = await cmsService.createPage(pageData);
    res.status(201).json(page);
  } catch (error) {
    console.error('Error creating page:', error);
    res.status(500).json({ error: 'Failed to create page' });
  }
});

// Update page (admin only)
router.put('/admin/pages/:id', authenticateToken, [
  body('title').optional().notEmpty(),
  body('slug').optional().notEmpty(),
  body('content').optional().notEmpty(),
  body('meta_title').optional().isString(),
  body('meta_description').optional().isString(),
  body('is_published').optional().isBoolean()
], async (req: Request, res: Response): Promise<any> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const page = await cmsService.updatePage(req.params.id, req.body);
    res.json(page);
  } catch (error) {
    console.error('Error updating page:', error);
    res.status(500).json({ error: 'Failed to update page' });
  }
});

// Delete page (admin only)
router.delete('/admin/pages/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    await cmsService.deletePage(req.params.id);
    res.json({ message: 'Page deleted successfully' });
  } catch (error) {
    console.error('Error deleting page:', error);
    res.status(500).json({ error: 'Failed to delete page' });
  }
});

// Inquiry Management
// Get all inquiries for admin
router.get('/admin/inquiries', authenticateToken, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('status').optional().isIn(['new', 'contacted', 'closed']),
  query('product_id').optional().isUUID(),
  query('search').optional().isString()
], async (req: Request, res: Response): Promise<any> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const offset = (page - 1) * limit;

    const filters = {
      status: req.query.status as 'new' | 'contacted' | 'closed',
      product_id: req.query.product_id as string,
      search: req.query.search as string,
      limit,
      offset
    };

    const inquiries = await cmsService.getAllInquiries(filters);

    res.json({
      inquiries,
      pagination: {
        page,
        limit,
        hasMore: inquiries.length === limit
      }
    });
  } catch (error) {
    console.error('Error fetching admin inquiries:', error);
    res.status(500).json({ error: 'Failed to fetch inquiries' });
  }
});

// Get inquiry by ID for admin
router.get('/admin/inquiries/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    const inquiry = await cmsService.getInquiryById(req.params.id);
    res.json(inquiry);
  } catch (error) {
    console.error('Error fetching inquiry:', error);
    res.status(404).json({ error: 'Inquiry not found' });
  }
});

// Update inquiry status (admin only)
router.patch('/admin/inquiries/:id/status', authenticateToken, [
  body('status').isIn(['new', 'contacted', 'closed']).withMessage('Valid status is required')
], async (req: Request, res: Response): Promise<any> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const inquiry = await cmsService.updateInquiryStatus(req.params.id, req.body.status);
    res.json(inquiry);
  } catch (error) {
    console.error('Error updating inquiry status:', error);
    res.status(500).json({ error: 'Failed to update inquiry status' });
  }
});

// Delete inquiry (admin only)
router.delete('/admin/inquiries/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    await cmsService.deleteInquiry(req.params.id);
    res.json({ message: 'Inquiry deleted successfully' });
  } catch (error) {
    console.error('Error deleting inquiry:', error);
    res.status(500).json({ error: 'Failed to delete inquiry' });
  }
});

// Generate slug (admin only)
router.post('/admin/generate-slug', authenticateToken, [
  body('title').notEmpty().withMessage('Title is required'),
  body('table').isIn(['products', 'categories', 'content_pages']).withMessage('Valid table is required')
], async (req: Request, res: Response): Promise<any> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const slug = await cmsService.generateSlug(req.body.title, req.body.table);
    res.json({ slug });
  } catch (error) {
    console.error('Error generating slug:', error);
    res.status(500).json({ error: 'Failed to generate slug' });
  }
});

export default router;
