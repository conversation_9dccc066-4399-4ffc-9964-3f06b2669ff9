import React, { useState, useRef, useCallback } from 'react';
import { motion, PanInfo, useMotionValue, useTransform } from 'framer-motion';
import { RefreshCw } from 'lucide-react';

interface PullToRefreshProps {
  children: React.ReactNode;
  onRefresh: () => Promise<void>;
  refreshThreshold?: number;
  maxPullDistance?: number;
  disabled?: boolean;
  className?: string;
}

const PullToRefresh: React.FC<PullToRefreshProps> = ({
  children,
  onRefresh,
  refreshThreshold = 80,
  maxPullDistance = 120,
  disabled = false,
  className = '',
}) => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [canRefresh, setCanRefresh] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Motion values
  const y = useMotionValue(0);
  const opacity = useTransform(y, [0, refreshThreshold], [0, 1]);
  const scale = useTransform(y, [0, refreshThreshold], [0.8, 1]);
  const rotate = useTransform(y, [0, refreshThreshold * 2], [0, 360]);

  // Check if we're at the top of the page
  const isAtTop = useCallback(() => {
    if (!containerRef.current) return false;
    return containerRef.current.scrollTop === 0;
  }, []);

  // Handle pan gesture
  const handlePan = useCallback((event: MouseEvent | TouchEvent, info: PanInfo) => {
    if (disabled || isRefreshing || !isAtTop()) return;

    const pullDistance = Math.max(0, Math.min(info.offset.y, maxPullDistance));
    y.set(pullDistance);
    
    // Update refresh state based on pull distance
    const shouldRefresh = pullDistance >= refreshThreshold;
    if (shouldRefresh !== canRefresh) {
      setCanRefresh(shouldRefresh);
      
      // Haptic feedback when threshold is reached
      if (shouldRefresh && 'vibrate' in navigator) {
        navigator.vibrate(10);
      }
    }
  }, [disabled, isRefreshing, refreshThreshold, maxPullDistance, canRefresh, y]);

  // Handle pan end
  const handlePanEnd = useCallback(async () => {
    if (disabled || isRefreshing) return;

    if (canRefresh) {
      setIsRefreshing(true);
      
      try {
        await onRefresh();
      } catch (error) {
        console.error('Refresh failed:', error);
      } finally {
        setIsRefreshing(false);
        setCanRefresh(false);
      }
    }
    
    // Reset position
    y.set(0);
  }, [disabled, isRefreshing, canRefresh, onRefresh, y]);

  // Get refresh indicator text
  const getRefreshText = () => {
    if (isRefreshing) return 'Refreshing...';
    if (canRefresh) return 'Release to refresh';
    return 'Pull to refresh';
  };

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Refresh Indicator */}
      <motion.div
        className="absolute top-0 left-0 right-0 z-10 flex flex-col items-center justify-center bg-gradient-to-b from-blue-50 to-transparent"
        style={{
          height: maxPullDistance,
          y: y.get() - maxPullDistance,
          opacity,
        }}
      >
        <motion.div
          className="flex flex-col items-center justify-center p-4"
          style={{ scale }}
        >
          <motion.div
            className={`mb-2 ${isRefreshing ? 'animate-spin' : ''}`}
            style={{ rotate: isRefreshing ? 0 : rotate }}
          >
            <RefreshCw 
              className={`w-6 h-6 ${
                canRefresh ? 'text-blue-600' : 'text-gray-400'
              }`} 
            />
          </motion.div>
          
          <motion.p
            className={`text-sm font-medium ${
              canRefresh ? 'text-blue-600' : 'text-gray-500'
            }`}
            animate={{
              color: canRefresh ? '#2563eb' : '#6b7280',
            }}
          >
            {getRefreshText()}
          </motion.p>
        </motion.div>
      </motion.div>

      {/* Content Container */}
      <motion.div
        ref={containerRef}
        className="h-full overflow-auto"
        style={{ y }}
        drag="y"
        dragConstraints={{ top: 0, bottom: 0 }}
        dragElastic={{ top: 0.3, bottom: 0 }}
        onPan={handlePan}
        onPanEnd={handlePanEnd}
        dragMomentum={false}
      >
        {children}
      </motion.div>

      {/* Loading Overlay */}
      {isRefreshing && (
        <motion.div
          className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-20"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <div className="flex flex-col items-center">
            <RefreshCw className="w-8 h-8 text-blue-600 animate-spin mb-2" />
            <p className="text-blue-600 font-medium">Refreshing...</p>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default PullToRefresh;
