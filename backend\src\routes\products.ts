import express, { Request, Response } from 'express';
import { body, query, validationResult } from 'express-validator';
import { productService } from '@/services/productService';
import { authenticateToken } from '@/middleware/auth';

const router = express.Router();

// Public routes - Frontend API
// Get all active products with filtering and pagination
router.get('/', [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('category_id').optional().isUUID(),
  query('search').optional().isString(),
  query('featured').optional().isBoolean()
], async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 12;
    const offset = (page - 1) * limit;

    const filters = {
      category_id: req.query.category_id as string,
      search: req.query.search as string,
      is_active: true,
      is_featured: req.query.featured === 'true' ? true : undefined,
      limit,
      offset
    };

    const products = await productService.getAllProducts(filters);

    res.json({
      products,
      pagination: {
        page,
        limit,
        hasMore: products.length === limit
      }
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({ error: 'Failed to fetch products' });
  }
});

// Get featured products
router.get('/featured', async (req: Request, res: Response) => {
  try {
    const limit = parseInt(req.query.limit as string) || 8;
    const products = await productService.getFeaturedProducts(limit);
    res.json(products);
  } catch (error) {
    console.error('Error fetching featured products:', error);
    res.status(500).json({ error: 'Failed to fetch featured products' });
  }
});

// Search products
router.get('/search', [
  query('q').notEmpty().withMessage('Search query is required'),
  query('limit').optional().isInt({ min: 1, max: 50 })
], async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const query = req.query.q as string;
    const limit = parseInt(req.query.limit as string) || 20;

    const products = await productService.searchProducts(query, limit);
    res.json(products);
  } catch (error) {
    console.error('Error searching products:', error);
    res.status(500).json({ error: 'Failed to search products' });
  }
});

// Get product by slug (public)
router.get('/slug/:slug', async (req: Request, res: Response) => {
  try {
    const product = await productService.getProductBySlug(req.params.slug);
    res.json(product);
  } catch (error) {
    console.error('Error fetching product by slug:', error);
    res.status(404).json({ error: 'Product not found' });
  }
});

// Get product by ID (public)
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const product = await productService.getProductById(req.params.id);
    res.json(product);
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(404).json({ error: 'Product not found' });
  }
});

// Admin routes - require authentication
// Get all products for admin (including inactive)
router.get('/admin/all', authenticateToken, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('category_id').optional().isUUID(),
  query('search').optional().isString(),
  query('is_active').optional().isBoolean(),
  query('is_featured').optional().isBoolean()
], async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const offset = (page - 1) * limit;

    const filters = {
      category_id: req.query.category_id as string,
      search: req.query.search as string,
      is_active: req.query.is_active === 'true' ? true : req.query.is_active === 'false' ? false : undefined,
      is_featured: req.query.is_featured === 'true' ? true : req.query.is_featured === 'false' ? false : undefined,
      limit,
      offset
    };

    const products = await productService.getAllProducts(filters);

    res.json({
      products,
      pagination: {
        page,
        limit,
        hasMore: products.length === limit
      }
    });
  } catch (error) {
    console.error('Error fetching admin products:', error);
    res.status(500).json({ error: 'Failed to fetch products' });
  }
});

// Create new product (admin only)
router.post('/', authenticateToken, [
  body('name').notEmpty().withMessage('Name is required'),
  body('description').notEmpty().withMessage('Description is required'),
  body('short_description').notEmpty().withMessage('Short description is required'),
  body('price').isFloat({ min: 0 }).withMessage('Price must be a positive number'),
  body('category_id').isUUID().withMessage('Valid category ID is required'),
  body('slug').notEmpty().withMessage('Slug is required'),
  body('images').isArray({ min: 1 }).withMessage('At least one image is required')
], async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const productData = {
      ...req.body,
      is_active: req.body.is_active ?? true,
      is_featured: req.body.is_featured ?? false
    };

    const product = await productService.createProduct(productData);
    res.status(201).json(product);
  } catch (error) {
    console.error('Error creating product:', error);
    res.status(500).json({ error: 'Failed to create product' });
  }
});

// Update product (admin only)
router.put('/:id', authenticateToken, [
  body('name').optional().notEmpty(),
  body('description').optional().notEmpty(),
  body('short_description').optional().notEmpty(),
  body('price').optional().isFloat({ min: 0 }),
  body('category_id').optional().isUUID(),
  body('slug').optional().notEmpty(),
  body('images').optional().isArray({ min: 1 })
], async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const product = await productService.updateProduct(req.params.id, req.body);
    res.json(product);
  } catch (error) {
    console.error('Error updating product:', error);
    res.status(500).json({ error: 'Failed to update product' });
  }
});

// Delete product (admin only)
router.delete('/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    await productService.deleteProduct(req.params.id);
    res.json({ message: 'Product deleted successfully' });
  } catch (error) {
    console.error('Error deleting product:', error);
    res.status(500).json({ error: 'Failed to delete product' });
  }
});

export default router;
