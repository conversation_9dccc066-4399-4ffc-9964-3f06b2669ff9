import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Loader2 } from 'lucide-react';

interface TouchFriendlyButtonProps {
  children: React.ReactNode;
  onClick?: () => void | Promise<void>;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  hapticFeedback?: boolean;
  rippleEffect?: boolean;
  className?: string;
  'data-analytics'?: string;
}

const TouchFriendlyButton: React.FC<TouchFriendlyButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  fullWidth = false,
  hapticFeedback = true,
  rippleEffect = true,
  className = '',
  'data-analytics': analyticsData,
}) => {
  const [isPressed, setIsPressed] = useState(false);
  const [ripples, setRipples] = useState<Array<{ id: number; x: number; y: number }>>([]);
  const [isLoading, setIsLoading] = useState(loading);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const rippleId = useRef(0);

  // Handle loading state
  useEffect(() => {
    setIsLoading(loading);
  }, [loading]);

  // Haptic feedback function
  const triggerHapticFeedback = () => {
    if (hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(10); // Short vibration
    }
  };

  // Handle touch start
  const handleTouchStart = (e: React.TouchEvent) => {
    if (disabled || isLoading) return;
    
    setIsPressed(true);
    triggerHapticFeedback();

    // Create ripple effect
    if (rippleEffect && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const touch = e.touches[0];
      const x = touch.clientX - rect.left;
      const y = touch.clientY - rect.top;
      
      const newRipple = {
        id: rippleId.current++,
        x,
        y,
      };
      
      setRipples(prev => [...prev, newRipple]);
      
      // Remove ripple after animation
      setTimeout(() => {
        setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
      }, 600);
    }
  };

  // Handle touch end
  const handleTouchEnd = () => {
    setIsPressed(false);
  };

  // Handle click
  const handleClick = async () => {
    if (disabled || isLoading || !onClick) return;

    try {
      setIsLoading(true);
      await onClick();
    } catch (error) {
      console.error('Button click error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Style variants
  const variants = {
    primary: 'bg-gold text-white hover:bg-gold/90 active:bg-gold/80 shadow-gold',
    secondary: 'bg-taupe text-white hover:bg-taupe/90 active:bg-taupe/80',
    outline: 'border-2 border-gold text-gold hover:bg-gold hover:text-white active:bg-gold/90',
    ghost: 'text-gold hover:bg-gold/10 active:bg-gold/20',
  };

  // Size variants
  const sizes = {
    sm: 'px-4 py-2 text-sm min-h-[36px]', // Minimum 36px for touch targets
    md: 'px-6 py-3 text-base min-h-[44px]', // Minimum 44px for touch targets
    lg: 'px-8 py-4 text-lg min-h-[52px]', // Larger for better accessibility
  };

  const baseClasses = `
    relative overflow-hidden rounded-lg font-medium
    transition-all duration-200 ease-out
    focus:outline-none focus:ring-2 focus:ring-gold/50 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
    select-none touch-manipulation
    ${variants[variant]}
    ${sizes[size]}
    ${fullWidth ? 'w-full' : ''}
    ${isPressed ? 'scale-95' : 'scale-100'}
    ${className}
  `;

  return (
    <motion.button
      ref={buttonRef}
      className={baseClasses}
      disabled={disabled || isLoading}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      onClick={handleClick}
      data-analytics={analyticsData}
      whileTap={{ scale: 0.95 }}
      transition={{ type: 'spring', stiffness: 400, damping: 17 }}
    >
      {/* Ripple effects */}
      <AnimatePresence>
        {ripples.map((ripple) => (
          <motion.span
            key={ripple.id}
            className="absolute bg-white/30 rounded-full pointer-events-none"
            style={{
              left: ripple.x - 10,
              top: ripple.y - 10,
              width: 20,
              height: 20,
            }}
            initial={{ scale: 0, opacity: 1 }}
            animate={{ scale: 4, opacity: 0 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.6, ease: 'easeOut' }}
          />
        ))}
      </AnimatePresence>

      {/* Button content */}
      <span className="relative flex items-center justify-center gap-2">
        {isLoading && (
          <Loader2 className="w-4 h-4 animate-spin" />
        )}
        {children}
      </span>
    </motion.button>
  );
};

export default TouchFriendlyButton;
