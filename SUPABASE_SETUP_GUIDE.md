# 🚀 WhiskAffair Supabase Setup Guide

## **Complete Migration from Prisma to Supabase**

Your WhiskAffair project has been successfully converted to use **Supabase** instead of Prisma. This guide will help you set up and deploy your Supabase database.

## 📋 **Prerequisites**

- Node.js 18+ and npm 8+
- Supabase account (free tier available)
- Basic knowledge of PostgreSQL and Supabase

## 🛠️ **Step 1: Create Supabase Project**

### **1.1 Sign up for Supabase**
1. Go to [supabase.com](https://supabase.com)
2. Click "Start your project"
3. Sign up with GitHub, Google, or email

### **1.2 Create New Project**
1. Click "New Project"
2. Choose your organization
3. Fill in project details:
   - **Name**: `whisk-affair`
   - **Database Password**: Generate a strong password (save it!)
   - **Region**: Choose closest to your users
   - **Pricing Plan**: Start with Free tier

### **1.3 Wait for Setup**
- Project creation takes 2-3 minutes
- You'll get a project dashboard when ready

## 🔑 **Step 2: Get Your Supabase Credentials**

### **2.1 Find Your Project Settings**
1. Go to your project dashboard
2. Click "Settings" in the sidebar
3. Click "API" tab

### **2.2 Copy Your Credentials**
You'll need these values:
- **Project URL**: `https://your-project-id.supabase.co`
- **Anon (public) key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
- **Service role (secret) key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

⚠️ **Important**: Keep your service role key secret! Never expose it in frontend code.

## 🗄️ **Step 3: Set Up Database Schema**

### **3.1 Run Database Migrations**
1. In your Supabase dashboard, go to "SQL Editor"
2. Click "New Query"
3. Copy and paste the content from `supabase/migrations/001_initial_schema.sql`
4. Click "Run" to create all tables
5. Repeat for `supabase/migrations/002_row_level_security.sql`

### **3.2 Verify Tables Created**
1. Go to "Table Editor" in your dashboard
2. You should see all tables:
   - `users`
   - `products`
   - `orders`
   - `analytics_events`
   - `security_logs`
   - And many more...

## ⚙️ **Step 4: Configure Environment Variables**

### **4.1 Backend Configuration**
Update your `server/.env` file:

```env
# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Other existing variables...
JWT_SECRET=your-jwt-secret
JWT_REFRESH_SECRET=your-refresh-secret
SESSION_SECRET=your-session-secret
REDIS_URL=redis://localhost:6379
```

### **4.2 Frontend Configuration**
Update your frontend `.env` file:

```env
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here

# API Configuration
VITE_API_BASE_URL=http://localhost:3001/api/v1
```

## 🔐 **Step 5: Configure Authentication**

### **5.1 Enable Email Authentication**
1. In Supabase dashboard, go to "Authentication" > "Settings"
2. Under "Auth Providers", ensure "Email" is enabled
3. Configure email templates if needed

### **5.2 Set Up Email Templates (Optional)**
1. Go to "Authentication" > "Email Templates"
2. Customize confirmation and reset password emails
3. Add your branding and styling

### **5.3 Configure Redirect URLs**
1. In "Authentication" > "URL Configuration"
2. Add your site URLs:
   - **Site URL**: `http://localhost:3000` (development)
   - **Redirect URLs**: 
     - `http://localhost:3000/auth/callback`
     - `https://your-domain.com/auth/callback` (production)

## 📦 **Step 6: Install Dependencies**

### **6.1 Backend Dependencies**
```bash
cd server
npm install @supabase/supabase-js
# Remove Prisma dependencies if desired
npm uninstall prisma @prisma/client
```

### **6.2 Frontend Dependencies**
```bash
# In your frontend directory
npm install @supabase/supabase-js
```

## 🚀 **Step 7: Start Your Application**

### **7.1 Start Backend Server**
```bash
cd server
npm run dev
```

### **7.2 Start Frontend**
```bash
# In your frontend directory
npm run dev
```

### **7.3 Test the Connection**
1. Open `http://localhost:3000`
2. Try registering a new user
3. Check your Supabase dashboard > "Authentication" > "Users"
4. You should see the new user appear

## 🔧 **Step 8: Advanced Configuration**

### **8.1 Set Up Real-time (Optional)**
1. In Supabase dashboard, go to "Database" > "Replication"
2. Enable replication for tables you want real-time updates:
   - `analytics_events`
   - `orders`
   - `products`

### **8.2 Configure Storage (For Product Images)**
1. Go to "Storage" in your dashboard
2. Create a new bucket called `product-images`
3. Set it to public if you want direct image access
4. Configure upload policies

### **8.3 Set Up Database Backups**
1. Go to "Settings" > "Database"
2. Enable automated backups
3. Choose backup frequency (daily recommended)

## 📊 **Step 9: Verify Analytics Integration**

### **9.1 Test Event Tracking**
1. Navigate through your app
2. In Supabase dashboard, go to "Table Editor" > "analytics_events"
3. You should see events being tracked in real-time

### **9.2 Check Admin Dashboard**
1. Go to `/admin` in your app
2. Verify analytics charts are working
3. Check that data is flowing from Supabase

## 🛡️ **Step 10: Security Best Practices**

### **10.1 Review Row Level Security**
1. In Supabase dashboard, go to "Authentication" > "Policies"
2. Verify RLS policies are active
3. Test that users can only access their own data

### **10.2 API Key Security**
- ✅ **Anon key**: Safe to use in frontend (public)
- ❌ **Service role key**: NEVER expose in frontend
- 🔒 **Use environment variables** for all keys

### **10.3 Database Security**
1. Enable database password requirements
2. Set up IP restrictions if needed
3. Monitor authentication logs

## 🚀 **Step 11: Production Deployment**

### **11.1 Production Environment Variables**
```env
# Production Supabase
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-production-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-production-service-key

# Production settings
NODE_ENV=production
```

### **11.2 Database Optimization**
1. Set up connection pooling in Supabase
2. Configure database indexes for performance
3. Set up monitoring and alerts

## 🔍 **Troubleshooting**

### **Common Issues**

#### **1. "Invalid API key" Error**
- Check your environment variables
- Ensure you're using the correct project URL
- Verify the API key format

#### **2. "Row Level Security" Errors**
- Check if RLS policies are properly configured
- Verify user authentication status
- Review policy conditions

#### **3. "Connection Failed" Error**
- Verify your Supabase project is active
- Check network connectivity
- Ensure correct project URL

#### **4. Authentication Not Working**
- Check redirect URLs configuration
- Verify email provider settings
- Review authentication policies

### **Getting Help**
- 📚 [Supabase Documentation](https://supabase.com/docs)
- 💬 [Supabase Discord Community](https://discord.supabase.com)
- 🐛 [GitHub Issues](https://github.com/supabase/supabase/issues)

## ✅ **Migration Checklist**

- [ ] Created Supabase project
- [ ] Copied API credentials
- [ ] Ran database migrations
- [ ] Updated environment variables
- [ ] Configured authentication
- [ ] Installed dependencies
- [ ] Tested user registration/login
- [ ] Verified analytics tracking
- [ ] Set up Row Level Security
- [ ] Configured storage (if needed)
- [ ] Tested admin dashboard
- [ ] Set up production environment

## 🎉 **Congratulations!**

Your WhiskAffair project is now running on **Supabase**! You have:

- ✅ **Modern PostgreSQL database** with real-time capabilities
- ✅ **Built-in authentication** with email/password and social providers
- ✅ **Row Level Security** for data protection
- ✅ **Real-time subscriptions** for live updates
- ✅ **File storage** for product images
- ✅ **Professional analytics** tracking
- ✅ **Scalable architecture** ready for production

Your e-commerce platform is now powered by one of the most modern and scalable database solutions available! 🚀
