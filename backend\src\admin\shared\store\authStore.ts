import { create } from 'zustand';

export interface User {
  id: string;
  email: string;
  role: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  verifyTwoFactor: (code: string) => Promise<void>;
  setToken: (token: string) => void;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  token: null,
  isAuthenticated: false,
  login: async (email: string, password: string) => {
    // Mock login implementation
    const mockUser: User = { id: '1', email, role: 'admin' };
    const mockToken = 'mock-token';
    set({ user: mockUser, token: mockToken, isAuthenticated: true });
  },
  logout: () => set({ user: null, token: null, isAuthenticated: false }),
  verifyTwoFactor: async (code: string) => {
    // Mock 2FA verification
    console.log('2FA verified with code:', code);
  },
  setToken: (token: string) => set({ token }),
}));