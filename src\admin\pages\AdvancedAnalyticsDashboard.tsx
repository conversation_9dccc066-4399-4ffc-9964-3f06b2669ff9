import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  ShoppingCart,
  Package,
  Eye,
  MousePointer,
  Clock,
  Globe,
  Smartphone,
  Monitor,
  Tablet,
  ArrowUpRight,
  ArrowDownRight,
  Filter,
  Download,
  RefreshCw,
  Calendar,
  MoreHorizontal,
} from 'lucide-react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ComposedChart,
  Scatter,
  ReferenceLine,
} from 'recharts';
import { cn } from '../utils/cn';
import Button, { IconButton } from '../components/ui/Button';

// Professional Analytics Dashboard - Shopify-level Quality
interface MetricCardProps {
  title: string;
  value: string;
  change: number;
  changeLabel: string;
  icon: React.ReactNode;
  trend: 'up' | 'down' | 'neutral';
  color: 'blue' | 'green' | 'purple' | 'orange' | 'red';
  loading?: boolean;
}

interface ChartData {
  name: string;
  value: number;
  previousValue?: number;
  conversion?: number;
  sessions?: number;
  revenue?: number;
  orders?: number;
}

const AdvancedAnalyticsDashboard: React.FC = () => {
  const [timeRange, setTimeRange] = useState<'24h' | '7d' | '30d' | '90d' | '1y'>('30d');
  const [loading, setLoading] = useState(false);
  const [selectedMetric, setSelectedMetric] = useState<string>('revenue');

  // Professional color palette
  const colors = {
    blue: '#3B82F6',
    green: '#10B981',
    purple: '#8B5CF6',
    orange: '#F59E0B',
    red: '#EF4444',
    gray: '#6B7280',
  };

  // Mock data - replace with real API calls
  const revenueData: ChartData[] = [
    { name: 'Jan', value: 12400, previousValue: 11200, sessions: 2400, orders: 124 },
    { name: 'Feb', value: 13200, previousValue: 12100, sessions: 2600, orders: 132 },
    { name: 'Mar', value: 14800, previousValue: 13400, sessions: 2900, orders: 148 },
    { name: 'Apr', value: 16200, previousValue: 14800, sessions: 3200, orders: 162 },
    { name: 'May', value: 18600, previousValue: 16900, sessions: 3700, orders: 186 },
    { name: 'Jun', value: 21400, previousValue: 19200, sessions: 4200, orders: 214 },
    { name: 'Jul', value: 23800, previousValue: 21600, sessions: 4700, orders: 238 },
    { name: 'Aug', value: 25200, previousValue: 23100, sessions: 5000, orders: 252 },
    { name: 'Sep', value: 27600, previousValue: 25400, sessions: 5500, orders: 276 },
    { name: 'Oct', value: 29800, previousValue: 27200, sessions: 5900, orders: 298 },
    { name: 'Nov', value: 32400, previousValue: 29800, sessions: 6400, orders: 324 },
    { name: 'Dec', value: 35600, previousValue: 32100, sessions: 7100, orders: 356 },
  ];

  const conversionFunnelData = [
    { name: 'Visitors', value: 10000, percentage: 100, color: colors.blue },
    { name: 'Product Views', value: 6500, percentage: 65, color: colors.green },
    { name: 'Add to Cart', value: 2600, percentage: 26, color: colors.purple },
    { name: 'Checkout', value: 1300, percentage: 13, color: colors.orange },
    { name: 'Purchase', value: 340, percentage: 3.4, color: colors.red },
  ];

  const deviceData = [
    { name: 'Mobile', value: 65, color: colors.blue },
    { name: 'Desktop', value: 25, color: colors.green },
    { name: 'Tablet', value: 10, color: colors.purple },
  ];

  const topProductsData = [
    { name: 'Chocolate Cake', revenue: 12400, orders: 124, growth: 12.5 },
    { name: 'Vanilla Cupcakes', revenue: 9800, orders: 98, growth: 8.2 },
    { name: 'Red Velvet', revenue: 8600, orders: 86, growth: -2.1 },
    { name: 'Lemon Tart', revenue: 7200, orders: 72, growth: 15.3 },
    { name: 'Strawberry Cake', revenue: 6800, orders: 68, growth: 5.7 },
  ];

  const cohortData = [
    { week: 'Week 1', retention: 100, users: 1000 },
    { week: 'Week 2', retention: 45, users: 450 },
    { week: 'Week 3', retention: 32, users: 320 },
    { week: 'Week 4', retention: 28, users: 280 },
    { week: 'Week 8', retention: 22, users: 220 },
    { week: 'Week 12', retention: 18, users: 180 },
  ];

  const MetricCard: React.FC<MetricCardProps> = ({
    title,
    value,
    change,
    changeLabel,
    icon,
    trend,
    color,
    loading = false,
  }) => {
    const colorClasses = {
      blue: 'bg-blue-50 text-blue-600 border-blue-100',
      green: 'bg-green-50 text-green-600 border-green-100',
      purple: 'bg-purple-50 text-purple-600 border-purple-100',
      orange: 'bg-orange-50 text-orange-600 border-orange-100',
      red: 'bg-red-50 text-red-600 border-red-100',
    };

    const trendIcon = trend === 'up' ? <ArrowUpRight className="w-4 h-4" /> : 
                     trend === 'down' ? <ArrowDownRight className="w-4 h-4" /> : null;

    const trendColor = trend === 'up' ? 'text-green-600' : 
                       trend === 'down' ? 'text-red-600' : 'text-gray-600';

    return (
      <motion.div
        className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-300"
        whileHover={{ y: -2 }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
      >
        <div className="flex items-center justify-between mb-4">
          <div className={cn('p-3 rounded-lg border', colorClasses[color])}>
            {icon}
          </div>
          <IconButton
            icon={<MoreHorizontal />}
            variant="ghost"
            size="sm"
            aria-label="More options"
          />
        </div>

        <div className="space-y-2">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          
          {loading ? (
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-24 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-16"></div>
            </div>
          ) : (
            <>
              <p className="text-3xl font-bold text-gray-900">{value}</p>
              <div className="flex items-center gap-2">
                <span className={cn('flex items-center gap-1 text-sm font-medium', trendColor)}>
                  {trendIcon}
                  {Math.abs(change)}%
                </span>
                <span className="text-sm text-gray-500">{changeLabel}</span>
              </div>
            </>
          )}
        </div>

        {/* Sparkline */}
        <div className="mt-4 h-12">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={revenueData.slice(-7)}>
              <Line
                type="monotone"
                dataKey="value"
                stroke={colors[color]}
                strokeWidth={2}
                dot={false}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </motion.div>
    );
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-900 mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <div key={index} className="flex items-center gap-2 text-sm">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-gray-600">{entry.dataKey}:</span>
              <span className="font-semibold text-gray-900">
                {typeof entry.value === 'number' && entry.dataKey === 'revenue'
                  ? `$${entry.value.toLocaleString()}`
                  : entry.value?.toLocaleString()}
              </span>
            </div>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-8 p-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Comprehensive insights into your business performance
          </p>
        </div>

        <div className="flex items-center gap-3">
          {/* Time Range Selector */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            {(['24h', '7d', '30d', '90d', '1y'] as const).map((range) => (
              <button
                key={range}
                onClick={() => setTimeRange(range)}
                className={cn(
                  'px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200',
                  timeRange === range
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                )}
              >
                {range}
              </button>
            ))}
          </div>

          <Button variant="outline" leftIcon={<Filter />}>
            Filter
          </Button>

          <Button variant="outline" leftIcon={<Download />}>
            Export
          </Button>

          <IconButton
            icon={<RefreshCw className={loading ? 'animate-spin' : ''} />}
            onClick={() => setLoading(!loading)}
            variant="outline"
            aria-label="Refresh data"
          />
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Revenue"
          value="$47,582"
          change={12.5}
          changeLabel="vs last month"
          icon={<DollarSign className="w-6 h-6" />}
          trend="up"
          color="green"
          loading={loading}
        />
        <MetricCard
          title="Orders"
          value="1,247"
          change={8.2}
          changeLabel="vs last month"
          icon={<ShoppingCart className="w-6 h-6" />}
          trend="up"
          color="blue"
          loading={loading}
        />
        <MetricCard
          title="Customers"
          value="892"
          change={-2.1}
          changeLabel="vs last month"
          icon={<Users className="w-6 h-6" />}
          trend="down"
          color="purple"
          loading={loading}
        />
        <MetricCard
          title="Conversion Rate"
          value="3.4%"
          change={0.8}
          changeLabel="vs last month"
          icon={<TrendingUp className="w-6 h-6" />}
          trend="up"
          color="orange"
          loading={loading}
        />
      </div>

      {/* Main Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Revenue Trend */}
        <div className="lg:col-span-2 bg-white rounded-xl border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Revenue Trend</h3>
              <p className="text-sm text-gray-600">Monthly revenue comparison</p>
            </div>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-2 text-sm">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="text-gray-600">Current Period</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
                <span className="text-gray-600">Previous Period</span>
              </div>
            </div>
          </div>

          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <ComposedChart data={revenueData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#6b7280' }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#6b7280' }}
                  tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
                />
                <Tooltip content={<CustomTooltip />} />
                <Area
                  type="monotone"
                  dataKey="previousValue"
                  fill="#e5e7eb"
                  stroke="#9ca3af"
                  strokeWidth={2}
                  fillOpacity={0.3}
                />
                <Area
                  type="monotone"
                  dataKey="value"
                  fill="#3b82f6"
                  stroke="#2563eb"
                  strokeWidth={3}
                  fillOpacity={0.1}
                />
                <Bar dataKey="orders" fill="#10b981" opacity={0.7} />
              </ComposedChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Conversion Funnel */}
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Conversion Funnel</h3>
            <p className="text-sm text-gray-600">Customer journey analysis</p>
          </div>

          <div className="space-y-4">
            {conversionFunnelData.map((step, index) => (
              <motion.div
                key={step.name}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="relative"
              >
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">{step.name}</span>
                  <div className="text-right">
                    <span className="text-sm font-semibold text-gray-900">
                      {step.value.toLocaleString()}
                    </span>
                    <span className="text-xs text-gray-500 ml-2">
                      {step.percentage}%
                    </span>
                  </div>
                </div>
                <div className="relative h-3 bg-gray-100 rounded-full overflow-hidden">
                  <motion.div
                    className="h-full rounded-full"
                    style={{ backgroundColor: step.color }}
                    initial={{ width: 0 }}
                    animate={{ width: `${step.percentage}%` }}
                    transition={{ duration: 1, delay: index * 0.2 }}
                  />
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Secondary Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Device Breakdown */}
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Device Breakdown</h3>
            <p className="text-sm text-gray-600">Traffic by device type</p>
          </div>

          <div className="flex items-center justify-center">
            <div className="w-64 h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={deviceData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={100}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {deviceData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>

          <div className="flex justify-center gap-6 mt-4">
            {deviceData.map((item) => (
              <div key={item.name} className="flex items-center gap-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: item.color }}
                />
                <span className="text-sm text-gray-600">
                  {item.name} ({item.value}%)
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Top Products</h3>
            <p className="text-sm text-gray-600">Best performing products</p>
          </div>

          <div className="space-y-4">
            {topProductsData.map((product, index) => (
              <motion.div
                key={product.name}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center text-white font-semibold text-sm">
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{product.name}</p>
                    <p className="text-sm text-gray-500">{product.orders} orders</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900">
                    ${product.revenue.toLocaleString()}
                  </p>
                  <p className={cn(
                    'text-sm font-medium',
                    product.growth > 0 ? 'text-green-600' : 'text-red-600'
                  )}>
                    {product.growth > 0 ? '+' : ''}{product.growth}%
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Customer Retention Cohort */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Customer Retention</h3>
          <p className="text-sm text-gray-600">Weekly cohort retention analysis</p>
        </div>

        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={cohortData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis
                dataKey="week"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#6b7280' }}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#6b7280' }}
                tickFormatter={(value) => `${value}%`}
              />
              <Tooltip content={<CustomTooltip />} />
              <Area
                type="monotone"
                dataKey="retention"
                stroke="#8b5cf6"
                fill="#8b5cf6"
                fillOpacity={0.2}
                strokeWidth={3}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default AdvancedAnalyticsDashboard;
