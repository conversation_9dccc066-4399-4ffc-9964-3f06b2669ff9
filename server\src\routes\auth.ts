import { Router, Request, Response, NextFunction } from 'express';
import { body, validationResult } from 'express-validator';
import rateLimit from 'express-rate-limit';

import { supabaseAuthService } from '../services/supabaseAuthService';
import { logger } from '../config/logger';

const router = Router();

// Rate limiting for auth endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: {
    success: false,
    error: 'Too many authentication attempts, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const registerLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 registrations per hour per IP
  message: {
    success: false,
    error: 'Too many registration attempts, please try again later.',
  },
});

// Validation middleware
const validateRegistration = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain uppercase, lowercase, number, and special character'),
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('phone')
    .optional()
    .isMobilePhone('any')
    .withMessage('Please provide a valid phone number'),
];

const validateLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
];

const validate2FA = [
  body('twoFactorToken')
    .notEmpty()
    .withMessage('2FA token is required'),
  body('code')
    .isLength({ min: 6, max: 6 })
    .isNumeric()
    .withMessage('2FA code must be 6 digits'),
];

// Helper function to handle validation errors
const handleValidationErrors = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// Helper function to get client info
const getClientInfo = (req: Request) => ({
  ipAddress: req.ip || req.connection.remoteAddress,
  userAgent: req.get('User-Agent'),
});

// POST /api/v1/auth/register
router.post('/register', 
  registerLimiter,
  validateRegistration,
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { email, password, firstName, lastName, phone } = req.body;
      const { ipAddress, userAgent } = getClientInfo(req);

      const result = await supabaseAuthService.register(
        { email, password, firstName, lastName, phone },
        ipAddress,
        userAgent
      );

      res.status(201).json({
        success: true,
        message: 'Registration successful',
        data: {
          user: result.user,
          session: result.session,
        },
      });
    } catch (error: any) {
      logger.error('Registration error:', error);
      next(error);
    }
  }
);

// POST /api/v1/auth/login
router.post('/login',
  authLimiter,
  validateLogin,
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { email, password, rememberMe } = req.body;
      const { ipAddress, userAgent } = getClientInfo(req);

      const result = await authService.login(
        { email, password, rememberMe },
        ipAddress,
        userAgent
      );

      // If 2FA is required
      if (result.requiresTwoFactor) {
        return res.status(200).json({
          success: true,
          message: '2FA verification required',
          data: {
            requiresTwoFactor: true,
            twoFactorToken: result.twoFactorToken,
          },
        });
      }

      // Set refresh token as httpOnly cookie
      const maxAge = rememberMe ? 30 * 24 * 60 * 60 * 1000 : 7 * 24 * 60 * 60 * 1000;
      res.cookie('refreshToken', result.tokens.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge,
      });

      res.status(200).json({
        success: true,
        message: 'Login successful',
        data: {
          user: result.user,
          accessToken: result.tokens.accessToken,
        },
      });
    } catch (error: any) {
      logger.error('Login error:', error);
      next(error);
    }
  }
);

// POST /api/v1/auth/verify-2fa
router.post('/verify-2fa',
  authLimiter,
  validate2FA,
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { twoFactorToken, code } = req.body;
      const { ipAddress, userAgent } = getClientInfo(req);

      const result = await authService.verify2FA(
        twoFactorToken,
        code,
        ipAddress,
        userAgent
      );

      // Set refresh token as httpOnly cookie
      res.cookie('refreshToken', result.tokens.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });

      res.status(200).json({
        success: true,
        message: '2FA verification successful',
        data: {
          user: result.user,
          accessToken: result.tokens.accessToken,
        },
      });
    } catch (error: any) {
      logger.error('2FA verification error:', error);
      next(error);
    }
  }
);

// POST /api/v1/auth/refresh
router.post('/refresh',
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const refreshToken = req.cookies.refreshToken || req.body.refreshToken;

      if (!refreshToken) {
        return res.status(401).json({
          success: false,
          error: 'Refresh token not provided',
        });
      }

      const tokens = await authService.refreshToken(refreshToken);

      // Update refresh token cookie
      res.cookie('refreshToken', tokens.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });

      res.status(200).json({
        success: true,
        message: 'Token refreshed successfully',
        data: {
          accessToken: tokens.accessToken,
        },
      });
    } catch (error: any) {
      logger.error('Token refresh error:', error);
      next(error);
    }
  }
);

// POST /api/v1/auth/logout
router.post('/logout',
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const refreshToken = req.cookies.refreshToken || req.body.refreshToken;
      const userId = (req as any).user?.id; // From auth middleware if available

      if (refreshToken) {
        await authService.logout(refreshToken, userId);
      }

      // Clear refresh token cookie
      res.clearCookie('refreshToken');

      res.status(200).json({
        success: true,
        message: 'Logout successful',
      });
    } catch (error: any) {
      logger.error('Logout error:', error);
      next(error);
    }
  }
);

// POST /api/v1/auth/setup-2fa (requires authentication)
router.post('/setup-2fa',
  // authMiddleware would be applied here
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }

      const result = await authService.setup2FA(userId);

      res.status(200).json({
        success: true,
        message: '2FA setup initiated',
        data: {
          secret: result.secret,
          qrCodeUrl: result.qrCodeUrl,
        },
      });
    } catch (error: any) {
      logger.error('2FA setup error:', error);
      next(error);
    }
  }
);

// POST /api/v1/auth/enable-2fa (requires authentication)
router.post('/enable-2fa',
  // authMiddleware would be applied here
  [
    body('code')
      .isLength({ min: 6, max: 6 })
      .isNumeric()
      .withMessage('2FA code must be 6 digits'),
  ],
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = (req as any).user?.id;
      const { code } = req.body;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }

      await authService.enable2FA(userId, code);

      res.status(200).json({
        success: true,
        message: '2FA enabled successfully',
      });
    } catch (error: any) {
      logger.error('2FA enable error:', error);
      next(error);
    }
  }
);

// POST /api/v1/auth/disable-2fa (requires authentication)
router.post('/disable-2fa',
  // authMiddleware would be applied here
  [
    body('code')
      .isLength({ min: 6, max: 6 })
      .isNumeric()
      .withMessage('2FA code must be 6 digits'),
  ],
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = (req as any).user?.id;
      const { code } = req.body;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }

      await authService.disable2FA(userId, code);

      res.status(200).json({
        success: true,
        message: '2FA disabled successfully',
      });
    } catch (error: any) {
      logger.error('2FA disable error:', error);
      next(error);
    }
  }
);

export default router;
