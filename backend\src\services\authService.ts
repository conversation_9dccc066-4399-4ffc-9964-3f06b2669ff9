import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import speakeasy from 'speakeasy';
import QRCode from 'qrcode';
import { v4 as uuidv4 } from 'uuid';
import { User } from '@/types/supabase';

// User role type for backward compatibility
type UserRole = 'admin' | 'customer';

import { supabase } from '@/config/supabase';
import { redis } from '@/config/redis';
import { config } from '@/config/environment';
import { logger, logSecurity } from '@/config/logger';
// import { AppError } from '@/utils/errors';

// Temporary AppError class until we create the utils/errors file
class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
}

export interface AuthResult {
  user: Omit<User, 'password'>;
  tokens: TokenPair;
  requiresTwoFactor?: boolean;
  twoFactorToken?: string;
}

export class AuthService {
  // Generate JWT tokens
  private generateTokens(userId: string, role: UserRole): TokenPair {
    const payload = { userId, role };
    
    const accessToken = jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn as string,
      issuer: 'whisk-affair',
      audience: 'whisk-affair-client',
    });

    const refreshToken = jwt.sign(payload, config.jwt.refreshSecret, {
      expiresIn: config.jwt.refreshExpiresIn as string,
      issuer: 'whisk-affair',
      audience: 'whisk-affair-client',
    });

    return { accessToken, refreshToken };
  }

  // Hash password
  private async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, config.security.bcryptRounds);
  }

  // Verify password
  private async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  // Generate 2FA secret
  private generate2FASecret(email: string): { secret: string; qrCode: string } {
    const secret = speakeasy.generateSecret({
      name: `WhiskAffair (${email})`,
      issuer: 'WhiskAffair',
      length: 32,
    });

    return {
      secret: secret.base32!,
      qrCode: secret.otpauth_url!,
    };
  }

  // Verify 2FA token
  private verify2FAToken(secret: string, token: string): boolean {
    return speakeasy.totp.verify({
      secret,
      encoding: 'base32',
      token,
      window: 2, // Allow 2 time steps (60 seconds) of tolerance
    });
  }

  // Check rate limiting
  private async checkRateLimit(
    identifier: string,
    maxAttempts: number,
    windowMs: number
  ): Promise<void> {
    const key = `rate_limit:${identifier}`;
    const attempts = await redis.incrementWithExpiry(key, Math.ceil(windowMs / 1000));

    if (attempts > maxAttempts) {
      const ttl = await redis.getClient().ttl(key);
      throw new AppError(
        `Too many attempts. Try again in ${Math.ceil(ttl / 60)} minutes.`,
        429
      );
    }
  }

  // Register new user
  async register(data: RegisterData, ipAddress?: string, userAgent?: string): Promise<AuthResult> {
    try {
      // Check rate limiting
      await this.checkRateLimit(
        `register:${ipAddress}`,
        5, // 5 registration attempts
        15 * 60 * 1000 // per 15 minutes
      );

      // Check if user already exists
      const { data: existingUser, error: findError } = await supabase.getAdminClient()
        .from('users')
        .select('*')
        .eq('email', data.email.toLowerCase())
        .single();

      if (findError && findError.code !== 'PGRST116') { // PGRST116 = no rows found
        throw findError;
      }

      if (existingUser) {
        throw new AppError('User already exists with this email', 409);
      }

      // Hash password
      const hashedPassword = await this.hashPassword(data.password);

      // Create user
      const { data: user, error: createError } = await supabase.getAdminClient()
        .from('users')
        .insert({
          email: data.email.toLowerCase(),
          password: hashedPassword,
          name: `${data.firstName} ${data.lastName}`,
          role: 'customer' as UserRole,
        })
        .select()
        .single();

      if (createError) throw createError;

      // Generate tokens
      const tokens = this.generateTokens(user.id, user.role);

      // Store refresh token
      const { error: tokenError } = await supabase.getAdminClient()
        .from('refresh_tokens')
        .insert({
          token: tokens.refreshToken,
          user_id: user.id,
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
        });

      if (tokenError) throw tokenError;

      // Log security event using Supabase analytics
      await supabase.getAdminClient()
        .from('analytics_events')
        .insert({
          event_type: 'user_registered',
          user_id: user.id,
          session_id: uuidv4(),
          properties: { email: user.email, method: 'email_password' },
          ip_address: ipAddress,
          user_agent: userAgent,
        });

      // Remove password from response
      const { password, ...userWithoutPassword } = user;

      return {
        user: userWithoutPassword,
        tokens,
      };
    } catch (error) {
      logger.error('Registration failed:', error);
      
      if (error instanceof AppError) {
        throw error;
      }
      
      throw new AppError('Registration failed', 500);
    }
  }

  // Login user
  async login(
    credentials: LoginCredentials,
    ipAddress?: string,
    userAgent?: string
  ): Promise<AuthResult> {
    try {
      const { email, password, rememberMe } = credentials;

      // Check rate limiting
      await this.checkRateLimit(
        `login:${email}`,
        config.security.authRateLimitMax,
        15 * 60 * 1000 // 15 minutes
      );

      // Find user
      const { data: user, error: findError } = await supabase.getAdminClient()
        .from('users')
        .select('*')
        .eq('email', email.toLowerCase())
        .single();

      if (findError && findError.code !== 'PGRST116') {
        throw findError;
      }

      if (!user) {
        // Log security event using Supabase analytics
        await supabase.getAdminClient()
          .from('analytics_events')
          .insert({
            event_type: 'login_failed',
            session_id: uuidv4(),
            properties: { email, reason: 'user_not_found', success: false },
            ip_address: ipAddress,
            user_agent: userAgent,
          });
        throw new AppError('Invalid credentials', 401);
      }

      // Check if account is locked
      if (user.locked_until && new Date(user.locked_until) > new Date()) {
        // Log security event using Supabase analytics
        await supabase.getAdminClient()
          .from('analytics_events')
          .insert({
            event_type: 'login_failed',
            user_id: user.id,
            session_id: uuidv4(),
            properties: { email, reason: 'account_locked', success: false },
            ip_address: ipAddress,
            user_agent: userAgent,
          });
        throw new AppError('Account is temporarily locked', 423);
      }

      // Verify password
      const isPasswordValid = await this.verifyPassword(password, user.password);

      if (!isPasswordValid) {
        // Increment failed attempts
        const failedAttempts = (user.login_attempts || 0) + 1;
        const shouldLock = failedAttempts >= 5;

        const { error: updateError } = await supabase.getAdminClient()
          .from('users')
          .update({
            login_attempts: failedAttempts,
            locked_until: shouldLock ? new Date(Date.now() + 15 * 60 * 1000).toISOString() : null, // 15 minutes
          })
          .eq('id', user.id);

        if (updateError) throw updateError;

        // Log security event using Supabase analytics
        await supabase.getAdminClient()
          .from('analytics_events')
          .insert({
            event_type: 'login_failed',
            user_id: user.id,
            session_id: uuidv4(),
            properties: { email, reason: 'invalid_password', attempts: failedAttempts, success: false },
            ip_address: ipAddress,
            user_agent: userAgent,
          });

        throw new AppError('Invalid credentials', 401);
      }

      // Check if 2FA is enabled
      if (user.two_factor_enabled) {
        // Generate temporary 2FA token
        const twoFactorToken = uuidv4();
        await redis.set(`2fa:${twoFactorToken}`, user.id, 300); // 5 minutes

        // Log security event using Supabase analytics
        await supabase.getAdminClient()
          .from('analytics_events')
          .insert({
            event_type: 'login_2fa_required',
            user_id: user.id,
            session_id: uuidv4(),
            properties: { email },
            ip_address: ipAddress,
            user_agent: userAgent,
          });

        const { password, ...userWithoutPassword } = user;

        return {
          user: userWithoutPassword,
          tokens: { accessToken: '', refreshToken: '' },
          requiresTwoFactor: true,
          twoFactorToken,
        };
      }

      // Reset failed attempts
      const { error: resetError } = await supabase.getAdminClient()
        .from('users')
        .update({
          login_attempts: 0,
          locked_until: null,
          last_login_at: new Date().toISOString(),
          last_login_ip: ipAddress,
        })
        .eq('id', user.id);

      if (resetError) throw resetError;

      // Generate tokens
      const tokens = this.generateTokens(user.id, user.role);

      // Store refresh token
      const expiresAt = rememberMe
        ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
        : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

      const { error: tokenError } = await supabase.getAdminClient()
        .from('refresh_tokens')
        .insert({
          token: tokens.refreshToken,
          user_id: user.id,
          expires_at: expiresAt.toISOString(),
        });

      if (tokenError) throw tokenError;

      // Log successful login using Supabase analytics
      await supabase.getAdminClient()
        .from('analytics_events')
        .insert({
          event_type: 'login_success',
          user_id: user.id,
          session_id: uuidv4(),
          properties: { email, rememberMe, success: true },
          ip_address: ipAddress,
          user_agent: userAgent,
        });

      // Remove password from response
      const { password: userPassword, ...userWithoutPassword } = user;

      return {
        user: userWithoutPassword,
        tokens,
      };
    } catch (error) {
      logger.error('Login failed:', error);
      
      if (error instanceof AppError) {
        throw error;
      }
      
      throw new AppError('Login failed', 500);
    }
  }

  // Verify 2FA and complete login
  async verify2FA(
    twoFactorToken: string,
    code: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<AuthResult> {
    try {
      // Get user ID from 2FA token
      const userId = await redis.get<string>(`2fa:${twoFactorToken}`);

      if (!userId) {
        throw new AppError('Invalid or expired 2FA token', 401);
      }

      // Get user
      const { data: user, error: findError } = await supabase.getAdminClient()
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (findError) throw findError;

      if (!user || !user.two_factor_secret) {
        throw new AppError('2FA not configured for this user', 400);
      }

      // Verify 2FA code
      const isValid = this.verify2FAToken(user.two_factor_secret, code);

      if (!isValid) {
        // Log security event using Supabase analytics
        await supabase.getAdminClient()
          .from('analytics_events')
          .insert({
            event_type: '2fa_failed',
            user_id: user.id,
            session_id: uuidv4(),
            properties: { code: code.replace(/./g, '*'), success: false },
            ip_address: ipAddress,
            user_agent: userAgent,
          });
        throw new AppError('Invalid 2FA code', 401);
      }

      // Delete 2FA token
      await redis.del(`2fa:${twoFactorToken}`);

      // Update user
      const { error: updateError } = await supabase.getAdminClient()
        .from('users')
        .update({
          login_attempts: 0,
          locked_until: null,
          last_login_at: new Date().toISOString(),
          last_login_ip: ipAddress,
        })
        .eq('id', user.id);

      if (updateError) throw updateError;

      // Generate tokens
      const tokens = this.generateTokens(user.id, user.role);

      // Store refresh token
      const { error: tokenError } = await supabase.getAdminClient()
        .from('refresh_tokens')
        .insert({
          token: tokens.refreshToken,
          user_id: user.id,
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
        });

      if (tokenError) throw tokenError;

      // Log successful 2FA using Supabase analytics
      await supabase.getAdminClient()
        .from('analytics_events')
        .insert({
          event_type: '2fa_success',
          user_id: user.id,
          session_id: uuidv4(),
          properties: { success: true },
          ip_address: ipAddress,
          user_agent: userAgent,
        });

      // Remove password from response
      const { password, ...userWithoutPassword } = user;

      return {
        user: userWithoutPassword,
        tokens,
      };
    } catch (error) {
      logger.error('2FA verification failed:', error);
      
      if (error instanceof AppError) {
        throw error;
      }
      
      throw new AppError('2FA verification failed', 500);
    }
  }

  // Refresh access token
  async refreshToken(refreshToken: string): Promise<TokenPair> {
    try {
      // Verify refresh token
      const payload = jwt.verify(refreshToken, config.jwt.refreshSecret) as any;

      // Check if refresh token exists in database
      const { data: storedToken, error: findError } = await supabase.getAdminClient()
        .from('refresh_tokens')
        .select(`
          *,
          users (*)
        `)
        .eq('token', refreshToken)
        .single();

      if (findError || !storedToken || new Date(storedToken.expires_at) < new Date()) {
        throw new AppError('Invalid or expired refresh token', 401);
      }

      // Generate new tokens
      const tokens = this.generateTokens(storedToken.users.id, storedToken.users.role);

      // Update refresh token in database
      const { error: updateError } = await supabase.getAdminClient()
        .from('refresh_tokens')
        .update({
          token: tokens.refreshToken,
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
        })
        .eq('id', storedToken.id);

      if (updateError) throw updateError;

      return tokens;
    } catch (error) {
      logger.error('Token refresh failed:', error);
      throw new AppError('Invalid refresh token', 401);
    }
  }

  // Logout user
  async logout(refreshToken: string, userId?: string): Promise<void> {
    try {
      // Remove refresh token from database
      let deleteQuery = supabase.getAdminClient()
        .from('refresh_tokens')
        .delete()
        .eq('token', refreshToken);

      if (userId) {
        deleteQuery = deleteQuery.eq('user_id', userId);
      }

      const { error: deleteError } = await deleteQuery;
      if (deleteError) throw deleteError;

      // Log logout using Supabase analytics
      if (userId) {
        await supabase.getAdminClient()
          .from('analytics_events')
          .insert({
            event_type: 'logout',
            user_id: userId,
            session_id: uuidv4(),
            properties: { success: true },
          });
      }
    } catch (error) {
      logger.error('Logout failed:', error);
      throw new AppError('Logout failed', 500);
    }
  }

  // Setup 2FA
  async setup2FA(userId: string): Promise<{ secret: string; qrCodeUrl: string }> {
    try {
      const { data: user, error: findError } = await supabase.getAdminClient()
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (findError || !user) {
        throw new AppError('User not found', 404);
      }

      // Generate 2FA secret
      const { secret, qrCode } = this.generate2FASecret(user.email);

      // Generate QR code
      const qrCodeUrl = await QRCode.toDataURL(qrCode);

      // Store secret temporarily (not enabled until verified)
      await redis.set(`2fa_setup:${userId}`, secret, 600); // 10 minutes

      return {
        secret,
        qrCodeUrl,
      };
    } catch (error) {
      logger.error('2FA setup failed:', error);
      throw new AppError('2FA setup failed', 500);
    }
  }

  // Enable 2FA
  async enable2FA(userId: string, code: string): Promise<void> {
    try {
      // Get temporary secret
      const secret = await redis.get<string>(`2fa_setup:${userId}`);

      if (!secret) {
        throw new AppError('2FA setup not found or expired', 400);
      }

      // Verify code
      const isValid = this.verify2FAToken(secret, code);

      if (!isValid) {
        throw new AppError('Invalid 2FA code', 400);
      }

      // Enable 2FA for user
      const { error: updateError } = await supabase.getAdminClient()
        .from('users')
        .update({
          two_factor_enabled: true,
          two_factor_secret: secret,
        })
        .eq('id', userId);

      if (updateError) throw updateError;

      // Remove temporary secret
      await redis.del(`2fa_setup:${userId}`);

      // Log 2FA enabled using Supabase analytics
      await supabase.getAdminClient()
        .from('analytics_events')
        .insert({
          event_type: '2fa_enabled',
          user_id: userId,
          session_id: uuidv4(),
          properties: { success: true },
        });
    } catch (error) {
      logger.error('2FA enable failed:', error);
      
      if (error instanceof AppError) {
        throw error;
      }
      
      throw new AppError('2FA enable failed', 500);
    }
  }

  // Disable 2FA
  async disable2FA(userId: string, code: string): Promise<void> {
    try {
      const { data: user, error: findError } = await supabase.getAdminClient()
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (findError || !user || !user.two_factor_secret) {
        throw new AppError('2FA not enabled for this user', 400);
      }

      // Verify code
      const isValid = this.verify2FAToken(user.two_factor_secret, code);

      if (!isValid) {
        throw new AppError('Invalid 2FA code', 400);
      }

      // Disable 2FA
      const { error: updateError } = await supabase.getAdminClient()
        .from('users')
        .update({
          two_factor_enabled: false,
          two_factor_secret: null,
        })
        .eq('id', userId);

      if (updateError) throw updateError;

      // Log 2FA disabled using Supabase analytics
      await supabase.getAdminClient()
        .from('analytics_events')
        .insert({
          event_type: '2fa_disabled',
          user_id: userId,
          session_id: uuidv4(),
          properties: { success: true },
        });
    } catch (error) {
      logger.error('2FA disable failed:', error);
      
      if (error instanceof AppError) {
        throw error;
      }
      
      throw new AppError('2FA disable failed', 500);
    }
  }
}

export const authService = new AuthService();
