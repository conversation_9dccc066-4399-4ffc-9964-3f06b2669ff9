import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import speakeasy from 'speakeasy';
import QRCode from 'qrcode';
import { v4 as uuidv4 } from 'uuid';
import { User } from '@/types/supabase';

import { supabase } from '@/config/supabase';
import { redis } from '@/config/redis';
import { config } from '@/config/environment';
import { logger, logSecurity } from '@/config/logger';
// import { AppError } from '@/utils/errors';

// Temporary AppError class until we create the utils/errors file
class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
}

export interface AuthResult {
  user: Omit<User, 'password'>;
  tokens: TokenPair;
  requiresTwoFactor?: boolean;
  twoFactorToken?: string;
}

export class AuthService {
  // Generate JWT tokens
  private generateTokens(userId: string, role: UserRole): TokenPair {
    const payload = { userId, role };
    
    const accessToken = jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn,
      issuer: 'whisk-affair',
      audience: 'whisk-affair-client',
    });

    const refreshToken = jwt.sign(payload, config.jwt.refreshSecret, {
      expiresIn: config.jwt.refreshExpiresIn,
      issuer: 'whisk-affair',
      audience: 'whisk-affair-client',
    });

    return { accessToken, refreshToken };
  }

  // Hash password
  private async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, config.security.bcryptRounds);
  }

  // Verify password
  private async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  // Generate 2FA secret
  private generate2FASecret(email: string): { secret: string; qrCode: string } {
    const secret = speakeasy.generateSecret({
      name: `WhiskAffair (${email})`,
      issuer: 'WhiskAffair',
      length: 32,
    });

    return {
      secret: secret.base32!,
      qrCode: secret.otpauth_url!,
    };
  }

  // Verify 2FA token
  private verify2FAToken(secret: string, token: string): boolean {
    return speakeasy.totp.verify({
      secret,
      encoding: 'base32',
      token,
      window: 2, // Allow 2 time steps (60 seconds) of tolerance
    });
  }

  // Check rate limiting
  private async checkRateLimit(
    identifier: string,
    maxAttempts: number,
    windowMs: number
  ): Promise<void> {
    const key = `rate_limit:${identifier}`;
    const attempts = await redis.incrementWithExpiry(key, Math.ceil(windowMs / 1000));

    if (attempts > maxAttempts) {
      const ttl = await redis.getClient().ttl(key);
      throw new AppError(
        `Too many attempts. Try again in ${Math.ceil(ttl / 60)} minutes.`,
        429
      );
    }
  }

  // Register new user
  async register(data: RegisterData, ipAddress?: string, userAgent?: string): Promise<AuthResult> {
    try {
      // Check rate limiting
      await this.checkRateLimit(
        `register:${ipAddress}`,
        5, // 5 registration attempts
        15 * 60 * 1000 // per 15 minutes
      );

      // Check if user already exists
      const existingUser = await db.user.findUnique({
        where: { email: data.email.toLowerCase() },
      });

      if (existingUser) {
        throw new AppError('User already exists with this email', 409);
      }

      // Hash password
      const hashedPassword = await this.hashPassword(data.password);

      // Create user
      const user = await db.user.create({
        data: {
          email: data.email.toLowerCase(),
          password: hashedPassword,
          firstName: data.firstName,
          lastName: data.lastName,
          phone: data.phone,
          role: UserRole.CUSTOMER,
        },
      });

      // Generate tokens
      const tokens = this.generateTokens(user.id, user.role);

      // Store refresh token
      await db.refreshToken.create({
        data: {
          token: tokens.refreshToken,
          userId: user.id,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        },
      });

      // Log security event
      await db.logSecurityEvent(
        'user_registered',
        { email: user.email, method: 'email_password' },
        user.id,
        ipAddress,
        userAgent
      );

      // Remove password from response
      const { password, ...userWithoutPassword } = user;

      return {
        user: userWithoutPassword,
        tokens,
      };
    } catch (error) {
      logger.error('Registration failed:', error);
      
      if (error instanceof AppError) {
        throw error;
      }
      
      throw new AppError('Registration failed', 500);
    }
  }

  // Login user
  async login(
    credentials: LoginCredentials,
    ipAddress?: string,
    userAgent?: string
  ): Promise<AuthResult> {
    try {
      const { email, password, rememberMe } = credentials;

      // Check rate limiting
      await this.checkRateLimit(
        `login:${email}`,
        config.security.authRateLimitMax,
        15 * 60 * 1000 // 15 minutes
      );

      // Find user
      const user = await db.user.findUnique({
        where: { email: email.toLowerCase() },
      });

      if (!user) {
        await db.logSecurityEvent(
          'login_failed',
          { email, reason: 'user_not_found' },
          undefined,
          ipAddress,
          userAgent,
          false
        );
        throw new AppError('Invalid credentials', 401);
      }

      // Check if account is locked
      if (user.lockedUntil && user.lockedUntil > new Date()) {
        await db.logSecurityEvent(
          'login_failed',
          { email, reason: 'account_locked' },
          user.id,
          ipAddress,
          userAgent,
          false
        );
        throw new AppError('Account is temporarily locked', 423);
      }

      // Verify password
      const isPasswordValid = await this.verifyPassword(password, user.password);

      if (!isPasswordValid) {
        // Increment failed attempts
        const failedAttempts = user.loginAttempts + 1;
        const shouldLock = failedAttempts >= 5;

        await db.user.update({
          where: { id: user.id },
          data: {
            loginAttempts: failedAttempts,
            lockedUntil: shouldLock ? new Date(Date.now() + 15 * 60 * 1000) : null, // 15 minutes
          },
        });

        await db.logSecurityEvent(
          'login_failed',
          { email, reason: 'invalid_password', attempts: failedAttempts },
          user.id,
          ipAddress,
          userAgent,
          false
        );

        throw new AppError('Invalid credentials', 401);
      }

      // Check if 2FA is enabled
      if (user.twoFactorEnabled) {
        // Generate temporary 2FA token
        const twoFactorToken = uuidv4();
        await redis.set(`2fa:${twoFactorToken}`, user.id, 300); // 5 minutes

        await db.logSecurityEvent(
          'login_2fa_required',
          { email },
          user.id,
          ipAddress,
          userAgent
        );

        const { password, ...userWithoutPassword } = user;

        return {
          user: userWithoutPassword,
          tokens: { accessToken: '', refreshToken: '' },
          requiresTwoFactor: true,
          twoFactorToken,
        };
      }

      // Reset failed attempts
      await db.user.update({
        where: { id: user.id },
        data: {
          loginAttempts: 0,
          lockedUntil: null,
          lastLoginAt: new Date(),
          lastLoginIp: ipAddress,
        },
      });

      // Generate tokens
      const tokens = this.generateTokens(user.id, user.role);

      // Store refresh token
      const expiresAt = rememberMe
        ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
        : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

      await db.refreshToken.create({
        data: {
          token: tokens.refreshToken,
          userId: user.id,
          expiresAt,
        },
      });

      // Log successful login
      await db.logSecurityEvent(
        'login_success',
        { email, rememberMe },
        user.id,
        ipAddress,
        userAgent
      );

      // Remove password from response
      const { password, ...userWithoutPassword } = user;

      return {
        user: userWithoutPassword,
        tokens,
      };
    } catch (error) {
      logger.error('Login failed:', error);
      
      if (error instanceof AppError) {
        throw error;
      }
      
      throw new AppError('Login failed', 500);
    }
  }

  // Verify 2FA and complete login
  async verify2FA(
    twoFactorToken: string,
    code: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<AuthResult> {
    try {
      // Get user ID from 2FA token
      const userId = await redis.get<string>(`2fa:${twoFactorToken}`);

      if (!userId) {
        throw new AppError('Invalid or expired 2FA token', 401);
      }

      // Get user
      const user = await db.user.findUnique({
        where: { id: userId },
      });

      if (!user || !user.twoFactorSecret) {
        throw new AppError('2FA not configured for this user', 400);
      }

      // Verify 2FA code
      const isValid = this.verify2FAToken(user.twoFactorSecret, code);

      if (!isValid) {
        await db.logSecurityEvent(
          '2fa_failed',
          { code: code.replace(/./g, '*') },
          user.id,
          ipAddress,
          userAgent,
          false
        );
        throw new AppError('Invalid 2FA code', 401);
      }

      // Delete 2FA token
      await redis.del(`2fa:${twoFactorToken}`);

      // Update user
      await db.user.update({
        where: { id: user.id },
        data: {
          loginAttempts: 0,
          lockedUntil: null,
          lastLoginAt: new Date(),
          lastLoginIp: ipAddress,
        },
      });

      // Generate tokens
      const tokens = this.generateTokens(user.id, user.role);

      // Store refresh token
      await db.refreshToken.create({
        data: {
          token: tokens.refreshToken,
          userId: user.id,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        },
      });

      // Log successful 2FA
      await db.logSecurityEvent(
        '2fa_success',
        {},
        user.id,
        ipAddress,
        userAgent
      );

      // Remove password from response
      const { password, ...userWithoutPassword } = user;

      return {
        user: userWithoutPassword,
        tokens,
      };
    } catch (error) {
      logger.error('2FA verification failed:', error);
      
      if (error instanceof AppError) {
        throw error;
      }
      
      throw new AppError('2FA verification failed', 500);
    }
  }

  // Refresh access token
  async refreshToken(refreshToken: string): Promise<TokenPair> {
    try {
      // Verify refresh token
      const payload = jwt.verify(refreshToken, config.jwt.refreshSecret) as any;

      // Check if refresh token exists in database
      const storedToken = await db.refreshToken.findUnique({
        where: { token: refreshToken },
        include: { user: true },
      });

      if (!storedToken || storedToken.expiresAt < new Date()) {
        throw new AppError('Invalid or expired refresh token', 401);
      }

      // Generate new tokens
      const tokens = this.generateTokens(storedToken.user.id, storedToken.user.role);

      // Update refresh token in database
      await db.refreshToken.update({
        where: { id: storedToken.id },
        data: {
          token: tokens.refreshToken,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        },
      });

      return tokens;
    } catch (error) {
      logger.error('Token refresh failed:', error);
      throw new AppError('Invalid refresh token', 401);
    }
  }

  // Logout user
  async logout(refreshToken: string, userId?: string): Promise<void> {
    try {
      // Remove refresh token from database
      await db.refreshToken.deleteMany({
        where: {
          token: refreshToken,
          ...(userId && { userId }),
        },
      });

      // Log logout
      if (userId) {
        await db.logSecurityEvent(
          'logout',
          {},
          userId
        );
      }
    } catch (error) {
      logger.error('Logout failed:', error);
      throw new AppError('Logout failed', 500);
    }
  }

  // Setup 2FA
  async setup2FA(userId: string): Promise<{ secret: string; qrCodeUrl: string }> {
    try {
      const user = await db.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new AppError('User not found', 404);
      }

      // Generate 2FA secret
      const { secret, qrCode } = this.generate2FASecret(user.email);

      // Generate QR code
      const qrCodeUrl = await QRCode.toDataURL(qrCode);

      // Store secret temporarily (not enabled until verified)
      await redis.set(`2fa_setup:${userId}`, secret, 600); // 10 minutes

      return {
        secret,
        qrCodeUrl,
      };
    } catch (error) {
      logger.error('2FA setup failed:', error);
      throw new AppError('2FA setup failed', 500);
    }
  }

  // Enable 2FA
  async enable2FA(userId: string, code: string): Promise<void> {
    try {
      // Get temporary secret
      const secret = await redis.get<string>(`2fa_setup:${userId}`);

      if (!secret) {
        throw new AppError('2FA setup not found or expired', 400);
      }

      // Verify code
      const isValid = this.verify2FAToken(secret, code);

      if (!isValid) {
        throw new AppError('Invalid 2FA code', 400);
      }

      // Enable 2FA for user
      await db.user.update({
        where: { id: userId },
        data: {
          twoFactorEnabled: true,
          twoFactorSecret: secret,
        },
      });

      // Remove temporary secret
      await redis.del(`2fa_setup:${userId}`);

      // Log 2FA enabled
      await db.logSecurityEvent(
        '2fa_enabled',
        {},
        userId
      );
    } catch (error) {
      logger.error('2FA enable failed:', error);
      
      if (error instanceof AppError) {
        throw error;
      }
      
      throw new AppError('2FA enable failed', 500);
    }
  }

  // Disable 2FA
  async disable2FA(userId: string, code: string): Promise<void> {
    try {
      const user = await db.user.findUnique({
        where: { id: userId },
      });

      if (!user || !user.twoFactorSecret) {
        throw new AppError('2FA not enabled for this user', 400);
      }

      // Verify code
      const isValid = this.verify2FAToken(user.twoFactorSecret, code);

      if (!isValid) {
        throw new AppError('Invalid 2FA code', 400);
      }

      // Disable 2FA
      await db.user.update({
        where: { id: userId },
        data: {
          twoFactorEnabled: false,
          twoFactorSecret: null,
        },
      });

      // Log 2FA disabled
      await db.logSecurityEvent(
        '2fa_disabled',
        {},
        userId
      );
    } catch (error) {
      logger.error('2FA disable failed:', error);
      
      if (error instanceof AppError) {
        throw error;
      }
      
      throw new AppError('2FA disable failed', 500);
    }
  }
}

export const authService = new AuthService();
