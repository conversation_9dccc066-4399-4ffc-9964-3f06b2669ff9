import { Router, Request, Response, NextFunction } from 'express';
import { body, query, validationResult } from 'express-validator';
import rateLimit from 'express-rate-limit';

import { analyticsService, AnalyticsEvent } from '@/services/analyticsService';
import { logger } from '@/config/logger';

const router = Router();

// Rate limiting for analytics endpoints
const analyticsLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 100, // 100 requests per minute
  message: {
    success: false,
    error: 'Too many analytics requests, please slow down.',
  },
});

const eventLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 1000, // 1000 events per minute
  message: {
    success: false,
    error: 'Too many events, please slow down.',
  },
});

// Validation middleware
const validateEvent = [
  body('eventType')
    .notEmpty()
    .isString()
    .withMessage('Event type is required and must be a string'),
  body('sessionId')
    .notEmpty()
    .isString()
    .withMessage('Session ID is required and must be a string'),
  body('properties')
    .isObject()
    .withMessage('Properties must be an object'),
  body('userId')
    .optional()
    .isString()
    .withMessage('User ID must be a string'),
  body('productId')
    .optional()
    .isString()
    .withMessage('Product ID must be a string'),
  body('page')
    .optional()
    .isString()
    .withMessage('Page must be a string'),
  body('referrer')
    .optional()
    .isString()
    .withMessage('Referrer must be a string'),
];

const validateBatchEvents = [
  body('events')
    .isArray({ min: 1, max: 100 })
    .withMessage('Events must be an array with 1-100 items'),
  body('events.*.eventType')
    .notEmpty()
    .isString()
    .withMessage('Each event must have a valid event type'),
  body('events.*.sessionId')
    .notEmpty()
    .isString()
    .withMessage('Each event must have a valid session ID'),
  body('events.*.properties')
    .isObject()
    .withMessage('Each event must have properties object'),
];

const validateDateRange = [
  query('startDate')
    .notEmpty()
    .isISO8601()
    .withMessage('Start date is required and must be a valid ISO date'),
  query('endDate')
    .notEmpty()
    .isISO8601()
    .withMessage('End date is required and must be a valid ISO date'),
];

// Helper function to handle validation errors
const handleValidationErrors = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// Helper function to get client info
const getClientInfo = (req: Request) => ({
  ipAddress: req.ip || req.connection.remoteAddress,
  userAgent: req.get('User-Agent'),
});

// POST /api/v1/analytics/events - Track single event
router.post('/events',
  eventLimiter,
  validateEvent,
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { ipAddress, userAgent } = getClientInfo(req);
      
      const event: AnalyticsEvent = {
        ...req.body,
        ipAddress,
        userAgent,
        timestamp: new Date().toISOString(),
      };

      await analyticsService.trackEvent(event);

      res.status(201).json({
        success: true,
        message: 'Event tracked successfully',
      });
    } catch (error: any) {
      logger.error('Failed to track analytics event:', error);
      next(error);
    }
  }
);

// POST /api/v1/analytics/events/batch - Track multiple events
router.post('/events/batch',
  eventLimiter,
  validateBatchEvents,
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { ipAddress, userAgent } = getClientInfo(req);
      const { events } = req.body;

      // Add client info to all events
      const enrichedEvents: AnalyticsEvent[] = events.map((event: any) => ({
        ...event,
        ipAddress,
        userAgent,
        timestamp: event.timestamp || new Date().toISOString(),
      }));

      await analyticsService.trackEvents(enrichedEvents);

      res.status(201).json({
        success: true,
        message: `${events.length} events tracked successfully`,
      });
    } catch (error: any) {
      logger.error('Failed to track analytics events batch:', error);
      next(error);
    }
  }
);

// GET /api/v1/analytics/metrics - Get analytics metrics (requires auth)
router.get('/metrics',
  analyticsLimiter,
  validateDateRange,
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      // This endpoint should require authentication in production
      // const userId = (req as any).user?.id;
      
      const { startDate, endDate, userId, eventType, productId } = req.query;

      const filters: Record<string, any> = {};
      if (userId) filters.userId = userId;
      if (eventType) filters.eventType = eventType;
      if (productId) filters.productId = productId;

      const metrics = await analyticsService.getMetrics(
        new Date(startDate as string),
        new Date(endDate as string),
        filters
      );

      res.status(200).json({
        success: true,
        data: metrics,
      });
    } catch (error: any) {
      logger.error('Failed to get analytics metrics:', error);
      next(error);
    }
  }
);

// GET /api/v1/analytics/products/:productId - Get product analytics (requires auth)
router.get('/products/:productId',
  analyticsLimiter,
  validateDateRange,
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { productId } = req.params;
      const { startDate, endDate } = req.query;

      const analytics = await analyticsService.getProductAnalytics(
        productId,
        new Date(startDate as string),
        new Date(endDate as string)
      );

      res.status(200).json({
        success: true,
        data: analytics,
      });
    } catch (error: any) {
      logger.error('Failed to get product analytics:', error);
      next(error);
    }
  }
);

// GET /api/v1/analytics/journey/:sessionId - Get user journey (requires auth)
router.get('/journey/:sessionId',
  analyticsLimiter,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { sessionId } = req.params;

      const journey = await analyticsService.getUserJourney(sessionId);

      if (!journey) {
        return res.status(404).json({
          success: false,
          error: 'User journey not found',
        });
      }

      res.status(200).json({
        success: true,
        data: journey,
      });
    } catch (error: any) {
      logger.error('Failed to get user journey:', error);
      next(error);
    }
  }
);

// GET /api/v1/analytics/pages/top - Get top pages (requires auth)
router.get('/pages/top',
  analyticsLimiter,
  validateDateRange,
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { startDate, endDate, limit = '10' } = req.query;

      const topPages = await analyticsService.getTopPages(
        new Date(startDate as string),
        new Date(endDate as string),
        parseInt(limit as string, 10)
      );

      res.status(200).json({
        success: true,
        data: topPages,
      });
    } catch (error: any) {
      logger.error('Failed to get top pages:', error);
      next(error);
    }
  }
);

// GET /api/v1/analytics/realtime - Get real-time analytics (requires auth)
router.get('/realtime',
  analyticsLimiter,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const realTimeData = await analyticsService.getRealTimeData();

      res.status(200).json({
        success: true,
        data: realTimeData,
      });
    } catch (error: any) {
      logger.error('Failed to get real-time analytics:', error);
      next(error);
    }
  }
);

// GET /api/v1/analytics/dashboard - Get dashboard data (requires auth)
router.get('/dashboard',
  analyticsLimiter,
  validateDateRange,
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { startDate, endDate } = req.query;
      const start = new Date(startDate as string);
      const end = new Date(endDate as string);

      // Get multiple analytics data in parallel
      const [metrics, topPages, realTimeData] = await Promise.all([
        analyticsService.getMetrics(start, end),
        analyticsService.getTopPages(start, end, 5),
        analyticsService.getRealTimeData(),
      ]);

      res.status(200).json({
        success: true,
        data: {
          metrics,
          topPages,
          realTime: realTimeData,
          dateRange: {
            startDate: start.toISOString(),
            endDate: end.toISOString(),
          },
        },
      });
    } catch (error: any) {
      logger.error('Failed to get dashboard analytics:', error);
      next(error);
    }
  }
);

// DELETE /api/v1/analytics/cleanup - Cleanup old events (requires admin auth)
router.delete('/cleanup',
  analyticsLimiter,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      // This should require admin authentication
      // const userRole = (req as any).user?.role;
      // if (userRole !== 'SUPER_ADMIN') {
      //   return res.status(403).json({
      //     success: false,
      //     error: 'Admin access required',
      //   });
      // }

      const { retentionDays = '365' } = req.query;
      const deletedCount = await analyticsService.cleanupOldEvents(
        parseInt(retentionDays as string, 10)
      );

      res.status(200).json({
        success: true,
        message: `Cleaned up ${deletedCount} old analytics events`,
        data: { deletedCount },
      });
    } catch (error: any) {
      logger.error('Failed to cleanup analytics events:', error);
      next(error);
    }
  }
);

// Health check endpoint
router.get('/health',
  async (req: Request, res: Response) => {
    res.status(200).json({
      success: true,
      message: 'Analytics service is healthy',
      timestamp: new Date().toISOString(),
    });
  }
);

export default router;
