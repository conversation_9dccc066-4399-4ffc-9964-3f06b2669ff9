import { Request, Response, NextFunction } from 'express';

// Basic validation middleware
export const validationMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Basic request validation
  if (req.body && typeof req.body === 'object') {
    // Remove any null prototype objects
    req.body = JSON.parse(JSON.stringify(req.body));
  }

  next();
};

export default validationMiddleware;
