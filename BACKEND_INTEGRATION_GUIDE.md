# WhiskAffair Backend Integration Guide

## 🚀 **Complete Backend Implementation**

This guide provides step-by-step instructions to implement the backend integration for your WhiskAffair e-commerce platform, connecting the analytics and security systems to real APIs.

## 📋 **Prerequisites**

- Node.js 18+ and npm 8+
- PostgreSQL database
- Redis server
- Basic knowledge of TypeScript and Express.js

## 🛠️ **Setup Instructions**

### **1. Install Dependencies**

```bash
cd server
npm install
```

### **2. Environment Configuration**

Copy the example environment file and configure it:

```bash
cp .env.example .env.local
```

Update the `.env.local` file with your actual values:

```env
# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/whisk_affair_db?schema=public"
REDIS_URL="redis://localhost:6379"

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Other configurations...
```

### **3. Database Setup**

Initialize Prisma and create the database:

```bash
# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma migrate dev --name init

# (Optional) Open Prisma Studio to view data
npx prisma studio
```

### **4. Start the Backend Server**

```bash
# Development mode
npm run dev

# Production mode
npm run build
npm start
```

The server will start on `http://localhost:3001`

## 🔗 **Frontend Integration**

### **1. Update Frontend Environment**

Create or update your frontend `.env.local` file:

```env
VITE_API_BASE_URL=http://localhost:3001/api/v1
VITE_SOCKET_URL=http://localhost:3001
```

### **2. Install Frontend Dependencies**

```bash
# In your frontend directory
npm install axios socket.io-client
```

### **3. Create API Client**

Create `src/services/api.ts`:

```typescript
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api/v1';

export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  withCredentials: true,
});

// Request interceptor to add auth token
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('accessToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for token refresh
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      try {
        const refreshResponse = await apiClient.post('/auth/refresh');
        const newToken = refreshResponse.data.data.accessToken;
        localStorage.setItem('accessToken', newToken);
        
        // Retry original request
        error.config.headers.Authorization = `Bearer ${newToken}`;
        return apiClient.request(error.config);
      } catch (refreshError) {
        // Redirect to login
        localStorage.removeItem('accessToken');
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);
```

### **4. Update Analytics Integration**

The analytics system is already configured to send events to the backend. The `CustomAnalyticsProvider` in `src/shared/analytics/index.ts` has been updated to:

- Send events to `/api/v1/analytics/events`
- Batch multiple events for efficiency
- Handle offline scenarios with local buffering
- Automatically retry failed requests

## 📊 **API Endpoints Available**

### **Authentication Endpoints**
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/verify-2fa` - 2FA verification
- `POST /api/v1/auth/refresh` - Token refresh
- `POST /api/v1/auth/logout` - User logout
- `POST /api/v1/auth/setup-2fa` - Setup 2FA
- `POST /api/v1/auth/enable-2fa` - Enable 2FA
- `POST /api/v1/auth/disable-2fa` - Disable 2FA

### **Analytics Endpoints**
- `POST /api/v1/analytics/events` - Track single event
- `POST /api/v1/analytics/events/batch` - Track multiple events
- `GET /api/v1/analytics/metrics` - Get analytics metrics
- `GET /api/v1/analytics/products/:productId` - Get product analytics
- `GET /api/v1/analytics/journey/:sessionId` - Get user journey
- `GET /api/v1/analytics/pages/top` - Get top pages
- `GET /api/v1/analytics/realtime` - Get real-time data
- `GET /api/v1/analytics/dashboard` - Get dashboard data

### **Health Check**
- `GET /api/v1/health` - System health check

## 🔒 **Security Features Implemented**

### **Authentication & Authorization**
- JWT with refresh token rotation
- Rate limiting on auth endpoints
- Account lockout after failed attempts
- 2FA support with TOTP
- Session validation and monitoring

### **Data Protection**
- Input validation with express-validator
- SQL injection prevention with Prisma
- XSS protection with helmet
- CSRF protection (token-based)
- Secure password hashing with bcrypt

### **Monitoring & Logging**
- Comprehensive security event logging
- Failed login attempt tracking
- API access monitoring
- Performance metrics collection

## 📈 **Analytics Features**

### **Event Tracking**
- Page views and user interactions
- Product views, clicks, and purchases
- Shopping cart events
- Search queries and results
- Form interactions and conversions

### **Real-time Analytics**
- Live user activity monitoring
- Real-time conversion tracking
- Active user counts
- Current page view statistics

### **Business Intelligence**
- Conversion funnel analysis
- Product performance metrics
- User journey mapping
- Revenue attribution
- Customer behavior insights

## 🧪 **Testing the Integration**

### **1. Test Authentication**

```bash
# Register a new user
curl -X POST http://localhost:3001/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "firstName": "Test",
    "lastName": "User"
  }'

# Login
curl -X POST http://localhost:3001/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!"
  }'
```

### **2. Test Analytics**

```bash
# Track an event
curl -X POST http://localhost:3001/api/v1/analytics/events \
  -H "Content-Type: application/json" \
  -d '{
    "eventType": "page_view",
    "sessionId": "session_123",
    "properties": {
      "page": "/products",
      "title": "Products Page"
    }
  }'

# Get metrics
curl "http://localhost:3001/api/v1/analytics/metrics?startDate=2024-01-01&endDate=2024-12-31"
```

### **3. Test Health Check**

```bash
curl http://localhost:3001/api/v1/health
```

## 🚀 **Production Deployment**

### **Environment Variables for Production**

```env
NODE_ENV=production
PORT=3001

# Use strong, unique secrets
JWT_SECRET=your-production-jwt-secret-64-chars-minimum
JWT_REFRESH_SECRET=your-production-refresh-secret-64-chars-minimum

# Production database
DATABASE_URL=your-production-database-url

# Production Redis
REDIS_URL=your-production-redis-url

# Email service (SendGrid, AWS SES, etc.)
SMTP_HOST=your-production-smtp-host
SMTP_USER=your-production-smtp-user
SMTP_PASS=your-production-smtp-password

# External services
STRIPE_SECRET_KEY=your-production-stripe-key
GOOGLE_ANALYTICS_ID=your-ga-measurement-id
```

### **Security Checklist**

- [ ] Use HTTPS in production
- [ ] Set secure environment variables
- [ ] Configure proper CORS origins
- [ ] Enable rate limiting
- [ ] Set up monitoring and alerting
- [ ] Configure log rotation
- [ ] Use a reverse proxy (nginx)
- [ ] Set up database backups
- [ ] Configure Redis persistence

## 📚 **Next Steps**

1. **Complete Product & Order APIs**: Implement full CRUD operations
2. **Payment Integration**: Add Stripe/PayPal payment processing
3. **Email Services**: Set up transactional emails
4. **File Upload**: Implement image upload for products
5. **Search**: Add Elasticsearch for product search
6. **Caching**: Implement Redis caching strategies
7. **Testing**: Add comprehensive test suite
8. **Documentation**: Generate API documentation with Swagger

## 🆘 **Troubleshooting**

### **Common Issues**

1. **Database Connection Failed**
   - Check PostgreSQL is running
   - Verify DATABASE_URL is correct
   - Ensure database exists

2. **Redis Connection Failed**
   - Check Redis server is running
   - Verify REDIS_URL is correct

3. **JWT Errors**
   - Ensure JWT secrets are set
   - Check token expiration times

4. **CORS Issues**
   - Verify CORS_ORIGIN includes your frontend URL
   - Check credentials are included in requests

### **Logs Location**
- Application logs: `logs/app.log`
- Security logs: `logs/security.log`
- Analytics logs: `logs/analytics.log`
- Performance logs: `logs/performance.log`

Your WhiskAffair backend is now ready for production with enterprise-grade security, comprehensive analytics, and scalable architecture!
