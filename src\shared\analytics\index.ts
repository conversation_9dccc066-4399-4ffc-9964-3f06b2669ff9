// Advanced Analytics and User Tracking System for WhiskAffair

import { AnalyticsEvent, AnalyticsEventType, ProductAnalytics } from '../types';

// ===== ANALYTICS MANAGER =====
class AnalyticsManager {
  private sessionId: string;
  private userId?: string;
  private events: AnalyticsEvent[] = [];
  private isEnabled: boolean = true;
  private providers: AnalyticsProvider[] = [];

  constructor() {
    this.sessionId = this.generateSessionId();
    this.initializeProviders();
    this.setupPageTracking();
    this.setupUserInteractionTracking();
  }

  // Initialize analytics providers
  private initializeProviders() {
    // Google Analytics 4
    if (typeof window !== 'undefined' && window.gtag) {
      this.providers.push(new GoogleAnalyticsProvider());
    }

    // Custom analytics provider for admin dashboard
    this.providers.push(new CustomAnalyticsProvider());

    // Facebook Pixel (if configured)
    if (typeof window !== 'undefined' && window.fbq) {
      this.providers.push(new FacebookPixelProvider());
    }
  }

  // Generate unique session ID
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Set user ID for tracking
  setUserId(userId: string) {
    this.userId = userId;
    this.providers.forEach(provider => provider.setUserId(userId));
  }

  // Track custom events
  track(eventType: AnalyticsEventType, properties: Record<string, any> = {}) {
    if (!this.isEnabled) return;

    const event: AnalyticsEvent = {
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      eventType,
      userId: this.userId,
      sessionId: this.sessionId,
      timestamp: new Date().toISOString(),
      properties,
      page: typeof window !== 'undefined' ? window.location.pathname : undefined,
      referrer: typeof document !== 'undefined' ? document.referrer : undefined,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
    };

    this.events.push(event);
    this.providers.forEach(provider => provider.track(event));

    // Store in localStorage for offline support
    this.storeEventLocally(event);
  }

  // Track page views
  trackPageView(page: string, title?: string) {
    this.track('page_view', {
      page,
      title: title || document.title,
      timestamp: Date.now(),
    });
  }

  // Track product interactions
  trackProductView(productId: string, productData: any) {
    this.track('product_view', {
      productId,
      productName: productData.name,
      category: productData.category,
      price: productData.price,
      timestamp: Date.now(),
    });
  }

  trackProductClick(productId: string, position: number, source: string) {
    this.track('product_click', {
      productId,
      position,
      source,
      timestamp: Date.now(),
    });
  }

  trackAddToCart(productId: string, quantity: number, price: number) {
    this.track('add_to_cart', {
      productId,
      quantity,
      price,
      value: quantity * price,
      timestamp: Date.now(),
    });
  }

  // Track user engagement
  trackScrollDepth(percentage: number, timeSpent: number) {
    this.track('scroll_depth', {
      percentage,
      timeSpent,
      page: window.location.pathname,
      timestamp: Date.now(),
    });
  }

  trackFormInteraction(formId: string, field: string, action: string) {
    this.track('form_interaction', {
      formId,
      field,
      action,
      timestamp: Date.now(),
    });
  }

  // Track search behavior
  trackSearch(query: string, resultsCount: number, filters?: Record<string, any>) {
    this.track('search', {
      query,
      resultsCount,
      filters,
      timestamp: Date.now(),
    });
  }

  // Store events locally for offline support
  private storeEventLocally(event: AnalyticsEvent) {
    try {
      const storedEvents = JSON.parse(localStorage.getItem('analytics_events') || '[]');
      storedEvents.push(event);
      
      // Keep only last 100 events to prevent storage overflow
      if (storedEvents.length > 100) {
        storedEvents.splice(0, storedEvents.length - 100);
      }
      
      localStorage.setItem('analytics_events', JSON.stringify(storedEvents));
    } catch (error) {
      console.warn('Failed to store analytics event locally:', error);
    }
  }

  // Setup automatic page tracking
  private setupPageTracking() {
    if (typeof window === 'undefined') return;

    // Track initial page load
    this.trackPageView(window.location.pathname);

    // Track navigation changes (for SPA)
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function(...args) {
      originalPushState.apply(history, args);
      setTimeout(() => analytics.trackPageView(window.location.pathname), 0);
    };

    history.replaceState = function(...args) {
      originalReplaceState.apply(history, args);
      setTimeout(() => analytics.trackPageView(window.location.pathname), 0);
    };

    window.addEventListener('popstate', () => {
      setTimeout(() => analytics.trackPageView(window.location.pathname), 0);
    });
  }

  // Setup automatic user interaction tracking
  private setupUserInteractionTracking() {
    if (typeof window === 'undefined') return;

    // Track scroll depth
    let maxScrollDepth = 0;
    let scrollStartTime = Date.now();

    window.addEventListener('scroll', () => {
      const scrollDepth = Math.round(
        (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
      );

      if (scrollDepth > maxScrollDepth) {
        maxScrollDepth = scrollDepth;
        
        // Track significant scroll milestones
        if (scrollDepth >= 25 && scrollDepth % 25 === 0) {
          this.trackScrollDepth(scrollDepth, Date.now() - scrollStartTime);
        }
      }
    });

    // Track clicks on product elements
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      const productElement = target.closest('[data-product-id]');
      
      if (productElement) {
        const productId = productElement.getAttribute('data-product-id');
        const position = Array.from(productElement.parentElement?.children || []).indexOf(productElement);
        const source = productElement.getAttribute('data-source') || 'unknown';
        
        if (productId) {
          this.trackProductClick(productId, position, source);
        }
      }
    });

    // Track form interactions
    document.addEventListener('focus', (event) => {
      const target = event.target as HTMLElement;
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.tagName === 'SELECT') {
        const form = target.closest('form');
        const formId = form?.id || form?.getAttribute('data-form-id') || 'unknown';
        const fieldName = target.getAttribute('name') || target.id || 'unknown';
        
        this.trackFormInteraction(formId, fieldName, 'focus');
      }
    });
  }

  // Get analytics data for admin dashboard
  getAnalyticsData(): AnalyticsEvent[] {
    return this.events;
  }

  // Enable/disable tracking
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
    this.providers.forEach(provider => provider.setEnabled(enabled));
  }
}

// ===== ANALYTICS PROVIDERS =====
abstract class AnalyticsProvider {
  abstract track(event: AnalyticsEvent): void;
  abstract setUserId(userId: string): void;
  abstract setEnabled(enabled: boolean): void;
}

class GoogleAnalyticsProvider extends AnalyticsProvider {
  private enabled = true;

  track(event: AnalyticsEvent) {
    if (!this.enabled || typeof window === 'undefined' || !window.gtag) return;

    window.gtag('event', event.eventType, {
      event_category: this.getEventCategory(event.eventType),
      event_label: event.properties.productId || event.properties.page,
      value: event.properties.value || event.properties.price,
      custom_parameters: event.properties,
    });
  }

  setUserId(userId: string) {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', 'GA_MEASUREMENT_ID', {
        user_id: userId,
      });
    }
  }

  setEnabled(enabled: boolean) {
    this.enabled = enabled;
  }

  private getEventCategory(eventType: AnalyticsEventType): string {
    const categoryMap: Record<string, string> = {
      page_view: 'engagement',
      product_view: 'ecommerce',
      product_click: 'ecommerce',
      add_to_cart: 'ecommerce',
      purchase: 'ecommerce',
      search: 'engagement',
      scroll_depth: 'engagement',
    };
    return categoryMap[eventType] || 'general';
  }
}

class CustomAnalyticsProvider extends AnalyticsProvider {
  private enabled = true;
  private apiEndpoint = '/api/v1/analytics/events';
  private batchEndpoint = '/api/v1/analytics/events/batch';
  private eventBuffer: AnalyticsEvent[] = [];
  private batchSize = 10;
  private flushInterval = 5000; // 5 seconds
  private flushTimer: NodeJS.Timeout | null = null;

  constructor() {
    super();
    this.startBatchTimer();
  }

  private startBatchTimer() {
    this.flushTimer = setInterval(() => {
      this.flushEvents();
    }, this.flushInterval);
  }

  track(event: AnalyticsEvent) {
    if (!this.enabled) return;

    // Add to buffer for batch processing
    this.eventBuffer.push(event);

    // Flush if buffer is full
    if (this.eventBuffer.length >= this.batchSize) {
      this.flushEvents();
    }
  }

  private async flushEvents() {
    if (this.eventBuffer.length === 0) return;

    const eventsToSend = [...this.eventBuffer];
    this.eventBuffer = [];

    try {
      const endpoint = eventsToSend.length === 1 ? this.apiEndpoint : this.batchEndpoint;
      const body = eventsToSend.length === 1
        ? JSON.stringify(eventsToSend[0])
        : JSON.stringify({ events: eventsToSend });

      await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body,
      });
    } catch (error) {
      console.warn('Failed to send analytics events:', error);
      // Put events back in buffer for retry
      this.eventBuffer.unshift(...eventsToSend);
    }
  }

  setUserId(userId: string) {
    // Custom implementation for user identification
  }

  setEnabled(enabled: boolean) {
    this.enabled = enabled;
  }
}

class FacebookPixelProvider extends AnalyticsProvider {
  private enabled = true;

  track(event: AnalyticsEvent) {
    if (!this.enabled || typeof window === 'undefined' || !window.fbq) return;

    const fbEventMap: Record<string, string> = {
      page_view: 'PageView',
      product_view: 'ViewContent',
      add_to_cart: 'AddToCart',
      purchase: 'Purchase',
      search: 'Search',
    };

    const fbEvent = fbEventMap[event.eventType];
    if (fbEvent) {
      window.fbq('track', fbEvent, event.properties);
    }
  }

  setUserId(userId: string) {
    if (typeof window !== 'undefined' && window.fbq) {
      window.fbq('init', 'FACEBOOK_PIXEL_ID', {
        external_id: userId,
      });
    }
  }

  setEnabled(enabled: boolean) {
    this.enabled = enabled;
  }
}

// ===== REACT HOOKS =====
export const useAnalytics = () => {
  return {
    track: analytics.track.bind(analytics),
    trackPageView: analytics.trackPageView.bind(analytics),
    trackProductView: analytics.trackProductView.bind(analytics),
    trackProductClick: analytics.trackProductClick.bind(analytics),
    trackAddToCart: analytics.trackAddToCart.bind(analytics),
    trackScrollDepth: analytics.trackScrollDepth.bind(analytics),
    trackFormInteraction: analytics.trackFormInteraction.bind(analytics),
    trackSearch: analytics.trackSearch.bind(analytics),
    setUserId: analytics.setUserId.bind(analytics),
    setEnabled: analytics.setEnabled.bind(analytics),
  };
};

// ===== GLOBAL ANALYTICS INSTANCE =====
export const analytics = new AnalyticsManager();

// ===== TYPE DECLARATIONS =====
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    fbq: (...args: any[]) => void;
  }
}

export default analytics;
