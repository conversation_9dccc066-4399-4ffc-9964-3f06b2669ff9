import React, { useState, useRef } from 'react';
import { motion, PanInfo, useMotionValue, useTransform } from 'framer-motion';
import { Heart, ShoppingCart, Eye, Share2 } from 'lucide-react';
import { useAnalytics } from '../../analytics';
import { Product } from '../../types';

interface SwipeableProductCardProps {
  product: Product;
  onAddToCart?: (productId: string) => void;
  onToggleWishlist?: (productId: string) => void;
  onQuickView?: (productId: string) => void;
  onShare?: (productId: string) => void;
  className?: string;
}

const SwipeableProductCard: React.FC<SwipeableProductCardProps> = ({
  product,
  onAddToCart,
  onToggleWishlist,
  onQuickView,
  onShare,
  className = '',
}) => {
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showActions, setShowActions] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const { trackProductView, trackProductClick, trackAddToCart } = useAnalytics();

  // Motion values for swipe gestures
  const x = useMotionValue(0);
  const opacity = useTransform(x, [-100, 0, 100], [0.5, 1, 0.5]);
  const scale = useTransform(x, [-100, 0, 100], [0.95, 1, 0.95]);

  // Handle swipe gestures
  const handlePan = (event: MouseEvent | TouchEvent, info: PanInfo) => {
    const threshold = 50;
    
    if (Math.abs(info.offset.x) > threshold) {
      if (info.offset.x > 0) {
        // Swipe right - show actions
        setShowActions(true);
      } else {
        // Swipe left - next image or hide actions
        if (showActions) {
          setShowActions(false);
        } else {
          handleNextImage();
        }
      }
    }
  };

  const handlePanEnd = () => {
    x.set(0);
  };

  // Handle image navigation
  const handleNextImage = () => {
    if (product.images.length > 1) {
      setCurrentImageIndex((prev) => 
        prev === product.images.length - 1 ? 0 : prev + 1
      );
    }
  };

  const handlePrevImage = () => {
    if (product.images.length > 1) {
      setCurrentImageIndex((prev) => 
        prev === 0 ? product.images.length - 1 : prev - 1
      );
    }
  };

  // Handle actions
  const handleAddToCart = () => {
    onAddToCart?.(product.id);
    trackAddToCart(product.id, 1, product.price);
    
    // Haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(15);
    }
  };

  const handleToggleWishlist = () => {
    setIsWishlisted(!isWishlisted);
    onToggleWishlist?.(product.id);
    
    // Haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(10);
    }
  };

  const handleQuickView = () => {
    onQuickView?.(product.id);
    trackProductView(product.id, product);
  };

  const handleShare = () => {
    onShare?.(product.id);
    
    // Native share API if available
    if (navigator.share) {
      navigator.share({
        title: product.name,
        text: product.shortDescription,
        url: window.location.href,
      });
    }
  };

  const handleCardClick = () => {
    trackProductClick(product.id, 0, 'product-card');
  };

  // Format price
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  return (
    <motion.div
      ref={cardRef}
      className={`relative bg-white rounded-2xl shadow-soft overflow-hidden ${className}`}
      style={{ x, opacity, scale }}
      drag="x"
      dragConstraints={{ left: -100, right: 100 }}
      onPan={handlePan}
      onPanEnd={handlePanEnd}
      onClick={handleCardClick}
      data-product-id={product.id}
      data-source="product-grid"
      whileHover={{ y: -4, shadow: '0 8px 30px rgba(0,0,0,0.12)' }}
      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
    >
      {/* Image Container */}
      <div className="relative aspect-square overflow-hidden">
        {/* Main Product Image */}
        <motion.img
          src={product.images[currentImageIndex]?.url}
          alt={product.images[currentImageIndex]?.altText || product.name}
          className="w-full h-full object-cover"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        />

        {/* Image Navigation Dots */}
        {product.images.length > 1 && (
          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex gap-1">
            {product.images.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                }`}
                onClick={(e) => {
                  e.stopPropagation();
                  setCurrentImageIndex(index);
                }}
              />
            ))}
          </div>
        )}

        {/* Quick Actions Overlay */}
        <motion.div
          className="absolute inset-0 bg-black/20 flex items-center justify-center gap-3"
          initial={{ opacity: 0 }}
          animate={{ opacity: showActions ? 1 : 0 }}
          transition={{ duration: 0.2 }}
          style={{ pointerEvents: showActions ? 'auto' : 'none' }}
        >
          <motion.button
            className="bg-white/90 backdrop-blur-sm p-3 rounded-full shadow-lg"
            onClick={(e) => {
              e.stopPropagation();
              handleToggleWishlist();
            }}
            whileTap={{ scale: 0.9 }}
            initial={{ scale: 0 }}
            animate={{ scale: showActions ? 1 : 0 }}
            transition={{ delay: 0.1 }}
          >
            <Heart 
              className={`w-5 h-5 ${isWishlisted ? 'fill-red-500 text-red-500' : 'text-gray-600'}`} 
            />
          </motion.button>

          <motion.button
            className="bg-white/90 backdrop-blur-sm p-3 rounded-full shadow-lg"
            onClick={(e) => {
              e.stopPropagation();
              handleQuickView();
            }}
            whileTap={{ scale: 0.9 }}
            initial={{ scale: 0 }}
            animate={{ scale: showActions ? 1 : 0 }}
            transition={{ delay: 0.2 }}
          >
            <Eye className="w-5 h-5 text-gray-600" />
          </motion.button>

          <motion.button
            className="bg-white/90 backdrop-blur-sm p-3 rounded-full shadow-lg"
            onClick={(e) => {
              e.stopPropagation();
              handleShare();
            }}
            whileTap={{ scale: 0.9 }}
            initial={{ scale: 0 }}
            animate={{ scale: showActions ? 1 : 0 }}
            transition={{ delay: 0.3 }}
          >
            <Share2 className="w-5 h-5 text-gray-600" />
          </motion.button>
        </motion.div>

        {/* Sale Badge */}
        {product.compareAtPrice && product.compareAtPrice > product.price && (
          <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium">
            {Math.round(((product.compareAtPrice - product.price) / product.compareAtPrice) * 100)}% OFF
          </div>
        )}

        {/* Featured Badge */}
        {product.featured && (
          <div className="absolute top-2 right-2 bg-gold text-white px-2 py-1 rounded-full text-xs font-medium">
            Featured
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="p-4">
        <h3 className="font-playfair text-lg font-semibold text-gray-900 mb-1 line-clamp-2">
          {product.name}
        </h3>
        
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {product.shortDescription}
        </p>

        {/* Price */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <span className="text-xl font-bold text-gray-900">
              {formatPrice(product.price)}
            </span>
            {product.compareAtPrice && product.compareAtPrice > product.price && (
              <span className="text-sm text-gray-500 line-through">
                {formatPrice(product.compareAtPrice)}
              </span>
            )}
          </div>
        </div>

        {/* Add to Cart Button */}
        <motion.button
          className="w-full bg-gold text-white py-3 rounded-xl font-medium flex items-center justify-center gap-2 min-h-[44px]"
          onClick={(e) => {
            e.stopPropagation();
            handleAddToCart();
          }}
          whileTap={{ scale: 0.98 }}
          transition={{ type: 'spring', stiffness: 400, damping: 17 }}
        >
          <ShoppingCart className="w-4 h-4" />
          Add to Cart
        </motion.button>
      </div>

      {/* Swipe Indicator */}
      <motion.div
        className="absolute right-2 top-1/2 transform -translate-y-1/2 text-white/70"
        initial={{ opacity: 0, x: 10 }}
        animate={{ 
          opacity: showActions ? 0 : 1,
          x: showActions ? 10 : 0 
        }}
        transition={{ duration: 0.2 }}
      >
        <div className="flex flex-col items-center text-xs">
          <span>←</span>
          <span className="text-[10px]">Swipe</span>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default SwipeableProductCard;
