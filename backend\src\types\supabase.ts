export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          name: string
          role: 'admin' | 'customer'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          name: string
          role?: 'admin' | 'customer'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string
          role?: 'admin' | 'customer'
          created_at?: string
          updated_at?: string
        }
      }
      products: {
        Row: {
          id: string
          name: string
          description: string
          short_description: string
          price: number
          category_id: string
          images: string[]
          is_featured: boolean
          is_active: boolean
          seo_title: string | null
          seo_description: string | null
          slug: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description: string
          short_description: string
          price: number
          category_id: string
          images?: string[]
          is_featured?: boolean
          is_active?: boolean
          seo_title?: string | null
          seo_description?: string | null
          slug: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string
          short_description?: string
          price?: number
          category_id?: string
          images?: string[]
          is_featured?: boolean
          is_active?: boolean
          seo_title?: string | null
          seo_description?: string | null
          slug?: string
          created_at?: string
          updated_at?: string
        }
      }
      categories: {
        Row: {
          id: string
          name: string
          description: string | null
          slug: string
          image_url: string | null
          is_active: boolean
          sort_order: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          slug: string
          image_url?: string | null
          is_active?: boolean
          sort_order?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          slug?: string
          image_url?: string | null
          is_active?: boolean
          sort_order?: number
          created_at?: string
          updated_at?: string
        }
      }
      inquiries: {
        Row: {
          id: string
          product_id: string | null
          customer_name: string
          customer_email: string
          customer_phone: string | null
          message: string
          status: 'new' | 'contacted' | 'closed'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          product_id?: string | null
          customer_name: string
          customer_email: string
          customer_phone?: string | null
          message: string
          status?: 'new' | 'contacted' | 'closed'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          product_id?: string | null
          customer_name?: string
          customer_email?: string
          customer_phone?: string | null
          message?: string
          status?: 'new' | 'contacted' | 'closed'
          created_at?: string
          updated_at?: string
        }
      }
      content_pages: {
        Row: {
          id: string
          title: string
          slug: string
          content: string
          meta_title: string | null
          meta_description: string | null
          is_published: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          slug: string
          content: string
          meta_title?: string | null
          meta_description?: string | null
          is_published?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          slug?: string
          content?: string
          meta_title?: string | null
          meta_description?: string | null
          is_published?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      analytics_events: {
        Row: {
          id: string
          event_type: string
          user_id: string | null
          session_id: string
          data: any
          created_at: string
        }
        Insert: {
          id?: string
          event_type: string
          user_id?: string | null
          session_id: string
          data?: any
          created_at?: string
        }
        Update: {
          id?: string
          event_type?: string
          user_id?: string | null
          session_id?: string
          data?: any
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type Inserts<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type Updates<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']

// Convenience types
export type User = Tables<'users'>
export type Product = Tables<'products'>
export type Category = Tables<'categories'>
export type Inquiry = Tables<'inquiries'>
export type ContentPage = Tables<'content_pages'>
export type AnalyticsEvent = Tables<'analytics_events'>
